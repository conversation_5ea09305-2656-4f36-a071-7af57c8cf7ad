/**
 * Client-Side Portfolio Export System
 *
 * This module implements a pure client-side export system that generates
 * static HTML portfolios with embedded CSS styling. The implementation
 * went through several iterations before reaching this robust solution.
 *
 * Key Features:
 * - Pure client-side processing (no server dependencies)
 * - Multi-layered CSS loading strategy with fallbacks
 * - Theme-specific HTML generation matching exact CSS classes
 * - Alpine.js integration for mobile menu functionality
 * - ZIP file generation and direct browser download
 *
 * Architecture Benefits:
 * - No server timeouts or memory constraints
 * - Instant export generation with full debugging
 * - Pixel-perfect match with live portfolio versions
 * - Easy to maintain and extend for new themes
 *
 * @see TechnicalDoc.md Section 4.3 for complete implementation details
 */

import JSZip from 'jszip';
import { PortfolioData, Experience } from './types';

// Client-side export utility
export async function exportPortfolioAsZip(portfolioData: PortfolioData) {
  try {
    console.log('🎨 Starting portfolio export for template:', portfolioData.templateId);

    // Get the theme CSS from theme registry
    let themeCss = '';
    let cssUrl: string | null = null;

    try {
      const { getThemeCssUrl } = await import('@/themes/theme-registry');
      cssUrl = getThemeCssUrl(portfolioData.templateId);
      console.log('📁 CSS URL from theme registry:', cssUrl);

      if (cssUrl) {
        const response = await fetch(cssUrl);
        if (response.ok) {
          themeCss = await response.text();
          console.log('✅ CSS loaded successfully:', themeCss.length, 'characters');
        } else {
          console.error('❌ Failed to fetch CSS:', response.status, response.statusText);
          throw new Error(`Failed to fetch theme CSS: ${response.status} ${response.statusText}`);
        }
      } else {
        throw new Error(`No CSS URL found for theme: ${portfolioData.templateId}`);
      }
    } catch (error) {
      console.error('❌ Failed to load theme CSS:', error);
      throw new Error(`Theme CSS not available. Please run "npm run sync-themes" and try again.`);
    }

    // If still no CSS, include a comprehensive fallback
    if (!themeCss) {
      console.log('Using fallback CSS for template:', portfolioData.templateId);
      themeCss = getBasicFallbackCss(portfolioData.templateId);
    }

    console.log('Final CSS length:', themeCss.length);
    console.log('CSS preview (first 500 chars):', themeCss.substring(0, 500));

    // Generate the static HTML by rendering the current portfolio
    const staticHtml = await generateStaticHtml(portfolioData);

    // Create the full HTML document
    const fullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${portfolioData.userName}'s Portfolio</title>
    <style>
${themeCss}
    </style>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
</head>
<body>
    ${staticHtml}
</body>
</html>`;

    // Create a ZIP file
    const zip = new JSZip();
    
    // Add the HTML file
    zip.file('index.html', fullHtml);
    
    // Add the CSS file separately
    zip.file('styles.css', themeCss);
    
    // Add a README
    const readme = `# ${portfolioData.userName}'s Portfolio

This is a static export of ${portfolioData.userName}'s portfolio website.

## Files included:
- index.html: The main portfolio page with embedded CSS
- styles.css: Separate CSS file for the theme

## How to use:
1. Open index.html in any web browser
2. The portfolio will work offline and can be hosted on any web server

Generated on: ${new Date().toISOString()}
Theme: ${portfolioData.templateId}
`;
    
    zip.file('README.md', readme);

    // Generate and download the ZIP file
    const zipBlob = await zip.generateAsync({ type: 'blob' });
    
    // Create download link
    const url = URL.createObjectURL(zipBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${portfolioData.slug || 'portfolio'}.zip`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    return true;
  } catch (error) {
    console.error('Export failed:', error);
    throw new Error('Failed to export portfolio');
  }
}





// Fallback CSS for when theme CSS can't be loaded
function getBasicFallbackCss(templateId: string): string {
  if (templateId === 'modern-theme-v1') {
    return `
      .theme-modern-root { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; color: #333; }
      .theme-modern-navbar { position: fixed; top: 0; width: 100%; background: rgba(255,255,255,0.95); backdrop-filter: blur(10px); z-index: 50; padding: 1rem 0; }
      .theme-modern-navbar-container { max-width: 1200px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center; padding: 0 2rem; }
      .theme-modern-navbar-brand { font-weight: bold; font-size: 1.25rem; }
      .theme-modern-navbar-nav { display: flex; gap: 2rem; }
      .theme-modern-navbar-link { text-decoration: none; color: #333; font-weight: 500; }
      .theme-modern-hero { padding: 8rem 0 4rem; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
      .theme-modern-hero-container { max-width: 1200px; margin: 0 auto; padding: 0 2rem; }
      .theme-modern-hero-layout { display: grid; grid-template-columns: 1fr 1fr; gap: 4rem; align-items: center; }
      .theme-modern-hero-title { font-size: 3rem; font-weight: bold; margin-bottom: 1rem; }
      .theme-modern-hero-subtitle { font-size: 1.25rem; margin-bottom: 2rem; opacity: 0.9; }
      .theme-modern-hero-image { width: 320px; height: 320px; border-radius: 50%; object-fit: cover; }
      .theme-modern-about, .theme-modern-projects, .theme-modern-contact { padding: 4rem 0; }
      .theme-modern-about-container, .theme-modern-projects-container, .theme-modern-contact-container { max-width: 1200px; margin: 0 auto; padding: 0 2rem; }
      .theme-modern-about-title, .theme-modern-projects-title, .theme-modern-contact-title { font-size: 2.5rem; margin-bottom: 2rem; text-align: center; }
      .theme-modern-projects-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; }
      .theme-modern-project-card { background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
      .theme-modern-project-image { width: 100%; height: 200px; object-fit: cover; }
      .theme-modern-project-content { padding: 1.5rem; }
      .theme-modern-project-title { font-size: 1.25rem; font-weight: bold; margin-bottom: 0.5rem; }
      .theme-modern-contact-info { display: flex; flex-direction: column; gap: 1rem; align-items: center; }
      .theme-modern-contact-social { display: flex; gap: 1rem; margin-top: 2rem; }
      .theme-modern-social-link { padding: 0.5rem 1rem; background: #667eea; color: white; text-decoration: none; border-radius: 4px; }
      .theme-modern-footer { background: #333; color: white; padding: 2rem 0; text-align: center; }
      @media (max-width: 768px) {
        .theme-modern-hero-layout { grid-template-columns: 1fr; text-align: center; }
        .theme-modern-hero-title { font-size: 2rem; }
        .theme-modern-navbar-nav { display: none; }
      }
    `;
  } else if (templateId === 'creative-theme-v1') {
    return `
      .theme-creative-root {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
        line-height: 1.6;
        color: #111827;
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        background: white;
        overflow-x: hidden;
      }
      .theme-creative-root *, .theme-creative-root *::before, .theme-creative-root *::after { box-sizing: inherit; }
      .theme-creative-container { max-width: 1200px; margin: 0 auto; padding: 0 1rem; }
      .theme-creative-navbar {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 50;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
      }
      .theme-creative-navbar-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        max-width: 1200px;
        margin: 0 auto;
      }
      .theme-creative-navbar-brand {
        font-size: 1.5rem;
        font-weight: 700;
        color: #111827;
        text-decoration: none;
        transition: color 0.3s ease;
      }
      .theme-creative-navbar-nav { display: none; gap: 2rem; }
      @media (min-width: 768px) { .theme-creative-navbar-nav { display: flex; } }
      .theme-creative-navbar-link {
        color: #6b7280;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        position: relative;
        padding: 0.5rem 0;
      }
      .theme-creative-navbar-link:hover { color: #111827; }
      .theme-creative-hero {
        min-height: 100vh;
        display: flex;
        align-items: center;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        position: relative;
        overflow: hidden;
      }
      .theme-creative-hero-container {
        position: relative;
        z-index: 1;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 1rem;
      }
      .theme-creative-hero-layout {
        display: grid;
        gap: 3rem;
        align-items: center;
        min-height: 100vh;
        padding: 2rem 0;
      }
      @media (min-width: 1024px) {
        .theme-creative-hero-layout {
          grid-template-columns: 1fr 1fr;
          gap: 4rem;
        }
      }
      .theme-creative-hero-content { text-align: center; }
      @media (min-width: 1024px) { .theme-creative-hero-content { text-align: left; } }
      .theme-creative-hero-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: #1f2937;
        line-height: 1.2;
        margin-bottom: 1rem;
      }
      @media (min-width: 640px) { .theme-creative-hero-title { font-size: 3rem; } }
      @media (min-width: 1024px) { .theme-creative-hero-title { font-size: 3.75rem; } }
      .theme-creative-hero-name {
        background: linear-gradient(90deg, #2563eb, #7c3aed);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .theme-creative-hero-profession-text {
        font-size: 1.25rem;
        color: #6b7280;
        font-weight: 500;
        margin-bottom: 2rem;
      }
      @media (min-width: 640px) { .theme-creative-hero-profession-text { font-size: 1.5rem; } }
      .theme-creative-hero-description-text {
        font-size: 1.125rem;
        color: #6b7280;
        line-height: 1.6;
        max-width: 600px;
        margin: 0 auto 2rem;
      }
      @media (min-width: 1024px) { .theme-creative-hero-description-text { margin: 0 0 2rem; } }
      .theme-creative-hero-actions {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        align-items: center;
        margin-bottom: 2rem;
      }
      @media (min-width: 640px) {
        .theme-creative-hero-actions {
          flex-direction: row;
          justify-content: center;
        }
      }
      @media (min-width: 1024px) {
        .theme-creative-hero-actions {
          justify-content: flex-start;
        }
      }
      .theme-creative-hero-cta {
        background: linear-gradient(90deg, #2563eb, #7c3aed);
        color: white;
        padding: 0.75rem 2rem;
        border-radius: 0.5rem;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
      }
      .theme-creative-hero-cta:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px -3px rgba(37, 99, 235, 0.3);
      }
      .theme-creative-hero-image-container {
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .theme-creative-hero-image {
        width: 320px;
        height: 320px;
        border-radius: 50%;
        object-fit: cover;
        border: 4px solid white;
        box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.25);
      }
      @media (min-width: 640px) {
        .theme-creative-hero-image {
          width: 400px;
          height: 400px;
        }
      }
    `;
  }

  return `
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
    .portfolio-container { max-width: 1200px; margin: 0 auto; padding: 0; }
    .hero-section { text-align: center; padding: 4rem 0; background: #f8f9fa; margin-bottom: 2rem; }
    .hero-section h1 { font-size: 3rem; margin-bottom: 1rem; }
    .hero-section p { font-size: 1.25rem; color: #666; }
    .about-section, .experience-section, .contact-section { margin-bottom: 3rem; }
    .about-section h2, .experience-section h2, .contact-section h2 { font-size: 2rem; margin-bottom: 1rem; border-bottom: 2px solid #007bff; padding-bottom: 0.5rem; }
    .experience-item { background: #f8f9fa; padding: 1.5rem; margin-bottom: 1rem; border-radius: 8px; }
    .experience-item h3 { margin-bottom: 0.5rem; color: #007bff; }
    a { color: #007bff; text-decoration: none; }
    a:hover { text-decoration: underline; }
  `;
}

// Helper function to generate static HTML using template-based approach
async function generateStaticHtml(portfolioData: PortfolioData): Promise<string> {
  // Generate HTML based on the theme template
  if (portfolioData.templateId === 'modern-theme-v1') {
    return generateModernThemeHtml(portfolioData);
  } else if (portfolioData.templateId === 'creative-theme-v1') {
    return generateCreativeThemeHtml(portfolioData);
  }

  // Fallback to basic template
  return generateBasicHtml(portfolioData);
}

// Generate HTML for Modern theme
function generateModernThemeHtml(portfolioData: PortfolioData): string {
  return `
    <div class="theme-modern-root">
      <!-- Navigation -->
      <header x-data="{ isOpen: false }" class="theme-modern-navbar">
        <div class="theme-modern-navbar-container">
          <a href="#" class="theme-modern-navbar-brand">
            ${portfolioData.userName || "Portfolio"}
          </a>
          <nav class="theme-modern-navbar-nav">
            <a href="#about" class="theme-modern-navbar-link">About</a>
            <a href="#experience" class="theme-modern-navbar-link">Experience</a>
            <a href="#skills" class="theme-modern-navbar-link">Skills</a>
            <a href="#projects" class="theme-modern-navbar-link">Work</a>
            <a href="#contact" class="theme-modern-navbar-link">Contact</a>
          </nav>
          <div class="theme-modern-navbar-mobile-toggle">
            <button
              @click="isOpen = !isOpen"
              class="theme-modern-navbar-mobile-btn"
              :class="{ 'theme-modern-navbar-mobile-btn-open': isOpen }"
            >
              <span class="theme-modern-navbar-mobile-line"></span>
              <span class="theme-modern-navbar-mobile-line"></span>
              <span class="theme-modern-navbar-mobile-line"></span>
            </button>
          </div>
        </div>
        <div x-show="isOpen" class="theme-modern-navbar-mobile-menu">
          <nav class="theme-modern-navbar-mobile-nav">
            <a href="#about" class="theme-modern-navbar-mobile-link" @click="isOpen = false">About</a>
            <a href="#experience" class="theme-modern-navbar-mobile-link" @click="isOpen = false">Experience</a>
            <a href="#skills" class="theme-modern-navbar-mobile-link" @click="isOpen = false">Skills</a>
            <a href="#projects" class="theme-modern-navbar-mobile-link" @click="isOpen = false">Work</a>
            <a href="#contact" class="theme-modern-navbar-mobile-link" @click="isOpen = false">Contact</a>
          </nav>
        </div>
      </header>

      <main>
        <!-- Hero Section -->
        <section class="theme-modern-hero">
          <div class="theme-modern-hero-container">
            <div class="theme-modern-hero-layout">
              <div class="theme-modern-hero-image-container">
                <div class="theme-modern-hero-image-wrapper">
                  <img
                    src="${portfolioData.profileImageUrl || 'https://placehold.co/320x320/1a1a1a/ffffff?text=Profile'}"
                    alt="${portfolioData.userName}"
                    width="320"
                    height="320"
                    class="theme-modern-hero-image"
                  />
                </div>
              </div>
              <div class="theme-modern-hero-content">
                <h1 class="theme-modern-hero-title">${portfolioData.userName}</h1>
                <p class="theme-modern-hero-subtitle">${portfolioData.profession || 'Professional'}</p>
                <div class="theme-modern-hero-actions">
                  ${portfolioData.resumeUrl ? `
                    <a
                      href="${portfolioData.resumeUrl}"
                      target="_blank"
                      rel="noopener noreferrer"
                      class="theme-modern-btn theme-modern-btn-primary"
                    >
                      <svg class="theme-modern-hero-action-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="m3 16 4 4 4-4"/>
                        <path d="M7 20V4"/>
                      </svg>
                      <span>Download Resume</span>
                    </a>
                  ` : ''}
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- About Section -->
        <section id="about" class="theme-modern-about">
          <div class="theme-modern-about-container">
            <div class="theme-modern-about-content">
              <h2 class="theme-modern-about-title">About Me</h2>
              <p class="theme-modern-about-text">${portfolioData.about || 'Welcome to my portfolio!'}</p>
            </div>
          </div>
        </section>

        <!-- Experience Section -->
        ${(portfolioData.experiences && portfolioData.experiences.length > 0) ? `
        <section id="experience" class="theme-modern-experience">
          <div class="theme-modern-experience-container">
            <h2 class="theme-modern-experience-title">Experience</h2>
            <div class="theme-modern-experience-timeline">
              ${(portfolioData.experiences || []).map(experience => `
                <div class="theme-modern-experience-item">
                  <div class="theme-modern-experience-timeline-dot"></div>
                  <div class="theme-modern-experience-content">
                    <div class="theme-modern-experience-header">
                      <div class="theme-modern-experience-title-group">
                        <h3 class="theme-modern-experience-role">${experience.role}</h3>
                        <div class="theme-modern-experience-company-info">
                          <svg class="theme-modern-experience-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M3 21h18"/>
                            <path d="M5 21V7l8-4v18"/>
                            <path d="M19 21V11l-6-4"/>
                          </svg>
                          <span class="theme-modern-experience-company">${experience.company}</span>
                          ${experience.companyUrl ? `
                            <a href="${experience.companyUrl}" target="_blank" rel="noopener noreferrer" class="theme-modern-experience-company-link">
                              <svg class="theme-modern-experience-link-icon" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M7 17L17 7"/>
                                <path d="M7 7h10v10"/>
                              </svg>
                            </a>
                          ` : ''}
                        </div>
                      </div>
                      <div class="theme-modern-experience-meta">
                        <span class="theme-modern-experience-duration">${experience.duration}</span>
                        ${experience.location ? `
                          <div class="theme-modern-experience-location">
                            <svg class="theme-modern-experience-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                              <circle cx="12" cy="10" r="3"/>
                            </svg>
                            <span class="theme-modern-experience-location-text">${experience.location}</span>
                          </div>
                        ` : ''}
                      </div>
                    </div>
                    ${experience.description ? `
                      <p class="theme-modern-experience-description">${experience.description}</p>
                    ` : ''}
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
        </section>
        ` : ''}

        <!-- Skills Section -->
        ${(portfolioData.skills && portfolioData.skills.length > 0) ? `
        <section id="skills" class="theme-modern-skills">
          <div class="theme-modern-skills-container">
            <h2 class="theme-modern-skills-title">Skills & Technologies</h2>
            <div class="theme-modern-skills-content">
              ${Object.entries((portfolioData.skills || []).reduce((acc: Record<string, typeof portfolioData.skills>, skill) => {
                if (!acc[skill.category]) acc[skill.category] = [];
                acc[skill.category].push(skill);
                return acc;
              }, {} as Record<string, typeof portfolioData.skills>)).map(([category, skills]) => {
                // Get category icon SVG
                const getCategoryIcon = (cat: string) => {
                  switch (cat) {
                    case 'web-development':
                      return '<svg class="theme-modern-skill-category-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="16,18 22,12 16,6"/><polyline points="8,6 2,12 8,18"/></svg>';
                    case 'mobile-development':
                      return '<svg class="theme-modern-skill-category-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><rect x="2" y="3" width="20" height="14" rx="2" ry="2"/><line x1="8" y1="21" x2="16" y2="21"/><line x1="12" y1="17" x2="12" y2="21"/></svg>';
                    case 'design':
                      return '<svg class="theme-modern-skill-category-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="13.5" cy="6.5" r=".5"/><circle cx="17.5" cy="10.5" r=".5"/><circle cx="8.5" cy="7.5" r=".5"/><circle cx="6.5" cy="12.5" r=".5"/><path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z"/></svg>';
                    case 'data-science':
                      return '<svg class="theme-modern-skill-category-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><ellipse cx="12" cy="5" rx="9" ry="3"/><path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"/><path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"/></svg>';
                    case 'devops':
                      return '<svg class="theme-modern-skill-category-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/></svg>';
                    case 'marketing':
                    case 'business':
                    default:
                      return '<svg class="theme-modern-skill-category-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polygon points="12,2 2,7 12,12 22,7 12,2"/><polyline points="2,17 12,22 22,17"/><polyline points="2,12 12,17 22,12"/></svg>';
                  }
                };

                const getCategoryLabel = (cat: string) => {
                  switch (cat) {
                    case 'web-development': return 'Web Development';
                    case 'mobile-development': return 'Mobile Development';
                    case 'design': return 'Design';
                    case 'data-science': return 'Data Science';
                    case 'devops': return 'DevOps';
                    case 'marketing': return 'Marketing';
                    case 'business': return 'Business';
                    default: return 'Other';
                  }
                };

                return `
                <div class="theme-modern-skills-category">
                  <div class="theme-modern-skills-category-header">
                    ${getCategoryIcon(category)}
                    <h3 class="theme-modern-skills-category-title">${getCategoryLabel(category)}</h3>
                  </div>
                  <div class="theme-modern-skills-grid">
                    ${(skills as typeof portfolioData.skills).map((skill) => `
                      <div class="theme-modern-skill-item">
                        <div class="theme-modern-skill-header">
                          <h3 class="theme-modern-skill-name">${skill.name}</h3>
                        </div>
                      </div>
                    `).join('')}
                  </div>
                </div>
                `;
              }).join('')}
            </div>
          </div>
        </section>
        ` : ''}

        <!-- Projects Section -->
        <section id="projects" class="theme-modern-projects">
          <div class="theme-modern-projects-container">
            <h2 class="theme-modern-projects-title">My Work</h2>
            <div class="theme-modern-projects-grid">
              ${portfolioData.projects?.map(project => `
                <div class="theme-modern-project-card">
                  ${project.imageUrl ? `
                    <div class="theme-modern-project-image-container">
                      <img
                        src="${project.imageUrl}"
                        alt="${project.title}"
                        class="theme-modern-project-image"
                      />
                    </div>
                  ` : ''}
                  <div class="theme-modern-project-content">
                    <h3 class="theme-modern-project-title">${project.title}</h3>
                    <p class="theme-modern-project-description">${project.description}</p>
                    <div class="theme-modern-project-links">
                      ${project.liveUrl ? `
                        <a href="${project.liveUrl}" target="_blank" rel="noopener noreferrer" class="theme-modern-project-link">
                          Live Demo
                        </a>
                      ` : ''}
                      ${project.url ? `
                        <a href="${project.url}" target="_blank" rel="noopener noreferrer" class="theme-modern-project-link">
                          View Project
                        </a>
                      ` : ''}
                    </div>
                  </div>
                </div>
              `).join('') || '<p class="theme-modern-projects-empty">No projects added yet.</p>'}
            </div>
          </div>
        </section>

        <!-- Contact Section -->
        <section id="contact" class="theme-modern-contact">
          <div class="theme-modern-contact-container">
            <h2 class="theme-modern-contact-title">Get In Touch</h2>
            <p class="theme-modern-contact-subtitle">
              Ready to collaborate? Let's discuss your next project and bring your ideas to life.
            </p>

            <div class="theme-modern-contact-info">
              ${portfolioData.email ? `
                <div class="theme-modern-contact-item">
                  <svg class="theme-modern-contact-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                    <polyline points="22,6 12,13 2,6"/>
                  </svg>
                  <span class="theme-modern-contact-text">${portfolioData.email}</span>
                </div>
              ` : ''}
              ${portfolioData.phone ? `
                <div class="theme-modern-contact-item">
                  <svg class="theme-modern-contact-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                  </svg>
                  <span class="theme-modern-contact-text">${portfolioData.phone}</span>
                </div>
              ` : ''}
            </div>

            <div class="theme-modern-social-links">
              ${portfolioData.githubUrl ? `
                <div class="theme-modern-social-item">
                  <a href="${portfolioData.githubUrl}" target="_blank" rel="noopener noreferrer" class="theme-modern-social-link" aria-label="GitHub">
                    <svg class="theme-modern-social-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"/>
                    </svg>
                  </a>
                </div>
              ` : ''}
              ${portfolioData.linkedinUrl ? `
                <div class="theme-modern-social-item">
                  <a href="${portfolioData.linkedinUrl}" target="_blank" rel="noopener noreferrer" class="theme-modern-social-link" aria-label="LinkedIn">
                    <svg class="theme-modern-social-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"/>
                      <rect x="2" y="9" width="4" height="12"/>
                      <circle cx="4" cy="4" r="2"/>
                    </svg>
                  </a>
                </div>
              ` : ''}
              ${portfolioData.twitterUrl ? `
                <div class="theme-modern-social-item">
                  <a href="${portfolioData.twitterUrl}" target="_blank" rel="noopener noreferrer" class="theme-modern-social-link" aria-label="Twitter">
                    <svg class="theme-modern-social-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"/>
                    </svg>
                  </a>
                </div>
              ` : ''}
            </div>
          </div>
        </section>
      </main>

      <!-- Footer -->
      <footer class="theme-modern-footer">
        <div class="theme-modern-footer-container">
          <div class="theme-modern-footer-content">
            <p class="theme-modern-footer-text">
              © ${new Date().getFullYear()} ${portfolioData.userName}. All rights reserved.
            </p>
            <p class="theme-modern-footer-attribution">
              Powered by Profolify
            </p>
          </div>
        </div>
      </footer>
    </div>
  `;
}

// Generate HTML for Creative theme
function generateCreativeThemeHtml(portfolioData: PortfolioData): string {
  return `
    <div class="theme-creative-root">
      <!-- Navigation -->
      <header x-data="{ isOpen: false }" class="theme-creative-navbar">
        <div class="theme-creative-navbar-container">
          <a href="#" class="theme-creative-navbar-brand">
            ${portfolioData.userName || "Portfolio"}
          </a>
          <nav class="theme-creative-navbar-nav">
            <a href="#about" class="theme-creative-navbar-link">About</a>
            <a href="#experience" class="theme-creative-navbar-link">Experience</a>
            <a href="#skills" class="theme-creative-navbar-link">Skills</a>
            <a href="#work" class="theme-creative-navbar-link">Work</a>
            <a href="#contact" class="theme-creative-navbar-link">Contact</a>
          </nav>
          <button
            @click="isOpen = !isOpen"
            class="theme-creative-navbar-mobile-toggle"
            aria-label="Toggle menu"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="3" y1="6" x2="21" y2="6"></line>
              <line x1="3" y1="12" x2="21" y2="12"></line>
              <line x1="3" y1="18" x2="21" y2="18"></line>
            </svg>
          </button>
        </div>
        <div class="theme-creative-navbar-mobile-menu" :class="{ 'active': isOpen }">
          <nav class="theme-creative-navbar-mobile-nav">
            <a href="#about" class="theme-creative-navbar-mobile-link" @click="isOpen = false">About</a>
            <a href="#experience" class="theme-creative-navbar-mobile-link" @click="isOpen = false">Experience</a>
            <a href="#skills" class="theme-creative-navbar-mobile-link" @click="isOpen = false">Skills</a>
            <a href="#work" class="theme-creative-navbar-mobile-link" @click="isOpen = false">Work</a>
            <a href="#contact" class="theme-creative-navbar-mobile-link" @click="isOpen = false">Contact</a>
          </nav>
        </div>
      </header>

      <main>
        <!-- Hero Section -->
        <section class="theme-creative-hero">
          <div class="theme-creative-hero-container">
            <div class="theme-creative-hero-layout">
              <div class="theme-creative-hero-content">
                <div class="theme-creative-hero-text">
                  <h1 class="theme-creative-hero-title">
                    Hi, I'm <span class="theme-creative-hero-name">${portfolioData.userName}</span>
                  </h1>
                </div>
                <div class="theme-creative-hero-profession">
                  <p class="theme-creative-hero-profession-text">${portfolioData.profession || 'Professional'}</p>
                </div>
                <div class="theme-creative-hero-description">
                  <p class="theme-creative-hero-description-text">${portfolioData.bio || portfolioData.about || 'Welcome to my creative space!'}</p>
                </div>
                <div class="theme-creative-hero-actions">
                  <a href="#projects" class="theme-creative-btn theme-creative-btn-primary">View My Work</a>
                  ${portfolioData.resumeUrl ? `
                    <a href="${portfolioData.resumeUrl}" target="_blank" rel="noopener noreferrer" class="theme-creative-btn theme-creative-btn-outline">
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="m3 16 4 4 4-4"/>
                        <path d="M7 20V4"/>
                      </svg>
                      <span>Download Resume</span>
                    </a>
                  ` : ''}
                </div>
              </div>
              <div class="theme-creative-hero-image-container">
                <div class="theme-creative-hero-image-wrapper">
                  <img
                    src="${portfolioData.profileImageUrl || 'https://placehold.co/320x320/f3f4f6/6b7280?text=Profile'}"
                    alt="${portfolioData.userName}"
                    width="320"
                    height="320"
                    class="theme-creative-hero-image"
                  />
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- About Section -->
        <section id="about" class="theme-creative-about">
          <div class="theme-creative-container">
            <div class="theme-creative-about-container">
              <h2 class="theme-creative-about-title">About Me</h2>
              <div class="theme-creative-about-content">
                <p class="theme-creative-about-text">${portfolioData.bio || portfolioData.about || 'Welcome to my creative space!'}</p>
              </div>
            </div>
          </div>
        </section>

        <!-- Experience Section -->
        ${(portfolioData.experiences && portfolioData.experiences.length > 0) ? `
        <section id="experience" class="theme-creative-experience">
          <div class="theme-creative-container">
            <div class="theme-creative-experience-header">
              <h2 class="theme-creative-experience-title">Experience</h2>
              <div class="theme-creative-experience-subtitle">My professional journey and career highlights</div>
            </div>
            <div class="theme-creative-experience-timeline">
              ${(portfolioData.experiences || []).map(experience => `
                <div class="theme-creative-experience-item">
                  <div class="theme-creative-experience-timeline-marker">
                    <div class="theme-creative-experience-timeline-dot"></div>
                    <div class="theme-creative-experience-timeline-line"></div>
                  </div>
                  <div class="theme-creative-experience-content">
                    <div class="theme-creative-experience-card">
                      <div class="theme-creative-experience-header">
                        <div class="theme-creative-experience-title-section">
                          <h3 class="theme-creative-experience-role">${experience.role}</h3>
                          <div class="theme-creative-experience-company-row">
                            <span class="theme-creative-experience-company">${experience.company}</span>
                            ${experience.companyUrl ? `
                              <a href="${experience.companyUrl}" target="_blank" rel="noopener noreferrer" class="theme-creative-experience-company-link">
                                ↗
                              </a>
                            ` : ''}
                          </div>
                        </div>
                        <div class="theme-creative-experience-meta">
                          <span class="theme-creative-experience-duration">${experience.duration}</span>
                          ${experience.location ? `
                            <div class="theme-creative-experience-location">
                              <span class="theme-creative-experience-location-text">${experience.location}</span>
                            </div>
                          ` : ''}
                        </div>
                      </div>
                      ${experience.description ? `
                        <p class="theme-creative-experience-description">${experience.description}</p>
                      ` : ''}
                    </div>
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
        </section>
        ` : ''}

        <!-- Skills Section -->
        ${(portfolioData.skills && portfolioData.skills.length > 0) ? `
        <section id="skills" class="theme-creative-skills">
          <div class="theme-creative-container">
            <div class="theme-creative-skills-header">
              <h2 class="theme-creative-skills-title">Skills & Technologies</h2>
              <div class="theme-creative-skills-subtitle">Technologies and tools I work with</div>
            </div>
            <div class="theme-creative-skills-content">
              ${Object.entries((portfolioData.skills || []).reduce((acc: Record<string, typeof portfolioData.skills>, skill) => {
                if (!acc[skill.category]) acc[skill.category] = [];
                acc[skill.category].push(skill);
                return acc;
              }, {} as Record<string, typeof portfolioData.skills>)).map(([category, skills]) => `
                <div class="theme-creative-skills-category">
                  <div class="theme-creative-skills-category-header">
                    <h3 class="theme-creative-skills-category-title">${category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}</h3>
                  </div>
                  <div class="theme-creative-skills-tags">
                    ${(skills as typeof portfolioData.skills).map((skill) => `
                      <div class="theme-creative-skill-tag theme-creative-skill-tag-${skill.category}">
                        <div class="theme-creative-skill-tag-content">
                          <span class="theme-creative-skill-name">${skill.name}</span>
                        </div>
                      </div>
                    `).join('')}
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
        </section>
        ` : ''}

        <!-- Projects Section -->
        <section id="work" class="theme-creative-projects">
          <div class="theme-creative-container">
            <div class="theme-creative-projects-container">
              <h2 class="theme-creative-projects-title">My Work</h2>
              <div class="theme-creative-projects-grid">
                ${portfolioData.projects?.map((project) => `
                  <div class="theme-creative-project-card">
                    ${project.imageUrl ? `
                      <div class="theme-creative-project-image-container">
                        <img
                          src="${project.imageUrl}"
                          alt="${project.title}"
                          class="theme-creative-project-image"
                        />
                      </div>
                    ` : ''}
                    <div class="theme-creative-project-content">
                      <h3 class="theme-creative-project-title">${project.title}</h3>
                      <p class="theme-creative-project-description">${project.description}</p>
                      <div class="theme-creative-project-links">
                        ${project.liveUrl ? `
                          <a href="${project.liveUrl}" class="theme-creative-project-link" target="_blank" rel="noopener noreferrer">
                            Live Demo
                          </a>
                        ` : ''}
                        ${project.url ? `
                          <a href="${project.url}" class="theme-creative-project-link" target="_blank" rel="noopener noreferrer">
                            View Project
                          </a>
                        ` : ''}
                      </div>
                    </div>
                  </div>
                `).join('') || '<p class="theme-creative-no-projects">No projects added yet.</p>'}
              </div>
            </div>
          </div>
        </section>

        <!-- Contact Section -->
        <section id="contact" class="theme-creative-contact">
          <div class="theme-creative-contact-container">
            <div class="theme-creative-contact-header">
              <h2 class="theme-creative-contact-title">Let's Work Together</h2>
              <p class="theme-creative-contact-subtitle">
                Ready to bring your ideas to life? Let's discuss your next project and create something amazing together.
              </p>
              <div class="theme-creative-contact-divider"></div>
            </div>

            <div class="theme-creative-contact-content">
              <div class="theme-creative-contact-info">
                ${portfolioData.email ? `
                  <div class="theme-creative-contact-item">
                    <svg class="theme-creative-contact-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                      <polyline points="22,6 12,13 2,6"/>
                    </svg>
                    <div class="theme-creative-contact-item-content">
                      <span class="theme-creative-contact-label">Email</span>
                      <span class="theme-creative-contact-text">${portfolioData.email}</span>
                    </div>
                  </div>
                ` : ''}
                ${portfolioData.phone ? `
                  <div class="theme-creative-contact-item">
                    <svg class="theme-creative-contact-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                    </svg>
                    <div class="theme-creative-contact-item-content">
                      <span class="theme-creative-contact-label">Phone</span>
                      <span class="theme-creative-contact-text">${portfolioData.phone}</span>
                    </div>
                  </div>
                ` : ''}
              </div>

              <div class="theme-creative-social-links">
                <h3 class="theme-creative-social-title">Connect With Me</h3>
                <div class="theme-creative-social-grid">
                  ${portfolioData.githubUrl ? `
                    <div class="theme-creative-social-item">
                      <a href="${portfolioData.githubUrl}" target="_blank" rel="noopener noreferrer" class="theme-creative-social-link" aria-label="GitHub">
                        <svg class="theme-creative-social-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                          <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"/>
                        </svg>
                        <span class="theme-creative-social-label">GitHub</span>
                      </a>
                    </div>
                  ` : ''}
                  ${portfolioData.linkedinUrl ? `
                    <div class="theme-creative-social-item">
                      <a href="${portfolioData.linkedinUrl}" target="_blank" rel="noopener noreferrer" class="theme-creative-social-link" aria-label="LinkedIn">
                        <svg class="theme-creative-social-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                          <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"/>
                          <rect x="2" y="9" width="4" height="12"/>
                          <circle cx="4" cy="4" r="2"/>
                        </svg>
                        <span class="theme-creative-social-label">LinkedIn</span>
                      </a>
                    </div>
                  ` : ''}
                  ${portfolioData.twitterUrl ? `
                    <div class="theme-creative-social-item">
                      <a href="${portfolioData.twitterUrl}" target="_blank" rel="noopener noreferrer" class="theme-creative-social-link" aria-label="Twitter">
                        <svg class="theme-creative-social-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                          <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"/>
                        </svg>
                        <span class="theme-creative-social-label">Twitter</span>
                      </a>
                    </div>
                  ` : ''}
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      <!-- Footer -->
      <footer class="theme-creative-footer">
        <div class="theme-creative-footer-container">
          <div class="theme-creative-footer-content">
            <div class="theme-creative-footer-main">
              <div class="theme-creative-footer-brand">
                <h3 class="theme-creative-footer-name">${portfolioData.userName}</h3>
                <p class="theme-creative-footer-tagline">Creating digital experiences that matter</p>
              </div>
              <button class="theme-creative-footer-scroll-top" onclick="window.scrollTo({top: 0, behavior: 'smooth'})">
                <svg class="theme-creative-footer-scroll-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="m18 15-6-6-6 6"/>
                </svg>
              </button>
            </div>
            <div class="theme-creative-footer-bottom">
              <div class="theme-creative-footer-text">
                <span class="theme-creative-footer-copyright">© ${new Date().getFullYear()} ${portfolioData.userName}. All rights reserved.</span>
                <div class="theme-creative-footer-made-with">
                  Made with
                  <svg class="theme-creative-footer-heart" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
                  </svg>
                  and
                  <svg class="theme-creative-footer-sparkles" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M9.5 2l1.5 3 3 1.5-3 1.5L9.5 11 8 8 5 6.5 8 5 9.5 2zM19 10l-1 2-2 1 2 1 1 2 1-2 2-1-2-1-1-2zM19 2l-.5 1-.5-1-.5 1 .5 1 .5-1 .5 1-.5-1L19 2z"/>
                  </svg>
                </div>
              </div>
              <p class="theme-creative-footer-attribution">Powered by Profolify</p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  `;
}

// Basic fallback HTML
function generateBasicHtml(portfolioData: PortfolioData): string {
  return `
    <div class="portfolio-container">
      <header class="hero-section">
        <h1>${portfolioData.userName}</h1>
        <p>${portfolioData.profession || 'Professional Portfolio'}</p>
        ${portfolioData.profileImageUrl ? `<img src="${portfolioData.profileImageUrl}" alt="${portfolioData.userName}" />` : ''}
      </header>

      <section class="about-section">
        <h2>About</h2>
        <p>${portfolioData.about || 'Welcome to my portfolio!'}</p>
      </section>

      <section class="experience-section">
        <h2>Experience</h2>
        ${portfolioData.experiences?.map((exp: Experience) => `
          <div class="experience-item">
            <h3>${exp.role}</h3>
            <p>${exp.company} | ${exp.duration}</p>
          </div>
        `).join('') || '<p>No experience added yet.</p>'}
      </section>

      <section class="contact-section">
        <h2>Contact</h2>
        ${portfolioData.email ? `<p>Email: ${portfolioData.email}</p>` : ''}
        ${portfolioData.phone ? `<p>Phone: ${portfolioData.phone}</p>` : ''}
        ${[
          portfolioData.githubUrl ? `<a href="${portfolioData.githubUrl}" target="_blank" rel="noopener noreferrer">GitHub</a>` : '',
          portfolioData.linkedinUrl ? `<a href="${portfolioData.linkedinUrl}" target="_blank" rel="noopener noreferrer">LinkedIn</a>` : '',
          portfolioData.twitterUrl ? `<a href="${portfolioData.twitterUrl}" target="_blank" rel="noopener noreferrer">Twitter</a>` : ''
        ].filter(Boolean).join(' | ')}
      </section>
    </div>
  `;
}
