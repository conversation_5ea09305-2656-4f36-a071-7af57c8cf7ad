export interface Project {
  id: string;
  title: string;
  description: string;
  url?: string;
  liveUrl?: string;
  imageUrl?: string;
}

export interface Experience {
  id: string;
  role: string;
  company: string;
  duration: string; // Single field for user to enter any date format they want
  description?: string;
  location?: string;
  companyUrl?: string;
}

export interface Skill {
  id: string;
  name: string;
}

export interface SocialLinks {
  github?: string;
  linkedin?: string;
  twitter?: string;
}




export interface PortfolioData {
  uid: string;
  isPublished: boolean;
  slug: string;
  templateId: string;
  userName: string;
  profession: string;
  about?: string;
  bio?: string; // Bio field for themes
  qualifications?: string; // Qualifications field for about section
  profileImageUrl?: string;
  resumeUrl?: string;
  projects: Project[];
  experiences: Experience[];
  skills: Skill[];
  socials: SocialLinks;
  contactEmail: string;
  email?: string; // Contact email field
  phone?: string; // Phone field
  githubUrl?: string; // Direct GitHub URL
  linkedinUrl?: string; // Direct LinkedIn URL
  twitterUrl?: string; // Direct Twitter URL
}

export const defaultPortfolioData = (uid: string, email: string | null): PortfolioData => ({
  uid,
  isPublished: false,
  slug: uid, // Default slug is the user's ID
  templateId: 'creative-theme-v1',
  userName: 'Your Name',
  profession: 'Your Profession',
  contactEmail: email || '<EMAIL>',
  profileImageUrl: '',
  resumeUrl: '',
  projects: [],
  experiences: [],
  skills: [],
  socials: {
    github: 'https://github.com',
    linkedin: 'https://linkedin.com',
    twitter: 'https://twitter.com'
  },
});


// A type for upload mutations, makes passing it easier
export type UploadFunction = (vars: { file: File; type: 'profile' | 'resume' | 'project'; id?: string }) => void;

export interface ProfolifyThemeProps {
  isEditing: boolean;
  serverData?: PortfolioData; // For public, server-rendered pages
  onImageUpload?: UploadFunction; // For editor page
}

// Props for each section component
export interface SectionProps {
  isEditing: boolean;
  serverData?: PortfolioData;
  onImageUpload?: UploadFunction;
}