import React from "react";
import Link from "next/link";
import { Github, Twitter, Mail, Heart } from "lucide-react";

export function LandingFooter() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-backgroundSecondary border-t border-borderPrimary/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex flex-col md:flex-row items-center justify-between gap-6">
          {/* Brand section */}
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-xl bg-gradient-to-r from-brandPrimary to-brandSecondary flex items-center justify-center">
              <span className="text-white font-bold text-sm">P</span>
            </div>
            <span className="font-bold text-xl gradient-text">Profolify</span>
          </div>

          {/* Links */}
          <div className="flex items-center gap-6 text-sm">
            <Link href="#features" className="text-textSecondary hover:text-brandPrimary transition-colors duration-200">
              Features
            </Link>
            <Link href="#themes" className="text-textSecondary hover:text-brandPrimary transition-colors duration-200">
              Themes
            </Link>
            <Link href="#pricing" className="text-textSecondary hover:text-brandPrimary transition-colors duration-200">
              Pricing
            </Link>
          </div>

          {/* Social links */}
          <div className="flex items-center gap-3">
            <Link
              href="https://github.com"
              className="w-8 h-8 rounded-lg bg-backgroundPrimary/50 border border-borderPrimary/50 flex items-center justify-center text-textSecondary hover:text-brandPrimary hover:border-brandPrimary/50 transition-colors duration-200"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="GitHub"
            >
              <Github className="w-4 h-4" />
            </Link>
            <Link
              href="https://twitter.com"
              className="w-8 h-8 rounded-lg bg-backgroundPrimary/50 border border-borderPrimary/50 flex items-center justify-center text-textSecondary hover:text-brandPrimary hover:border-brandPrimary/50 transition-colors duration-200"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Twitter"
            >
              <Twitter className="w-4 h-4" />
            </Link>
            <Link
              href="mailto:<EMAIL>"
              className="w-8 h-8 rounded-lg bg-backgroundPrimary/50 border border-borderPrimary/50 flex items-center justify-center text-textSecondary hover:text-brandPrimary hover:border-brandPrimary/50 transition-colors duration-200"
              aria-label="Email"
            >
              <Mail className="w-4 h-4" />
            </Link>
          </div>
        </div>

        {/* Bottom section */}
        <div className="mt-8 pt-6 border-t border-borderPrimary/30 flex flex-col md:flex-row items-center justify-between gap-4 text-sm text-textSecondary">
          <p>© {currentYear} Profolify. All rights reserved.</p>
          <div className="flex items-center gap-1">
            <span>Built with</span>
            <Heart className="w-4 h-4 text-red-500 fill-current" />
            <span>for creators</span>
          </div>
        </div>
      </div>
    </footer>
  );
}

