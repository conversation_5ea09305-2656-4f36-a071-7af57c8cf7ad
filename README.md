# Profolify - The Modern Portfolio Builder

<div align="center">
  <img src="./public/logo.png" alt="Profolify Logo" width="150">
  <h1 align="center">Profolify</h1>
  <p align="center">
    Create, edit, and publish a beautiful, professional, and fast portfolio in minutes.
    <br />
    <br />
    <a href="#"><strong>Explore a Demo »</strong></a>
    <br />
    <br />
  </p>
</div>

## ✨ About The Project

Profolify is a revolutionary portfolio creation platform that empowers professionals to build stunning, responsive portfolios with zero coding required. Built with cutting-edge technology and featuring our groundbreaking **Live DOM Capture Export System**, Profolify delivers pixel-perfect static websites that work anywhere.

This project represents the next generation of portfolio builders, combining intuitive WYSIWYG editing with professional-grade export capabilities that preserve every detail of your live portfolio.

### 🚀 Revolutionary Features:

*   **Live DOM Capture Export:** Our breakthrough technology captures your actual rendered portfolio, ensuring exported sites match your live portfolio pixel-for-pixel
*   **Real-time Inline Editing:** Click directly on any text to edit it in place with instant visual feedback
*   **Professional Theme System:** Choose from beautifully crafted themes (Modern, Creative Minimalist) with more coming soon
*   **Smart URL Generation:** Automatically creates clean, SEO-friendly URLs from your name (e.g., `/john-smith`)
*   **Universal Static Export:** Download complete, self-contained websites ready for any hosting service
*   **Google Authentication:** Secure, one-click sign-in with your Google account
*   **Cloudinary Integration:** Professional image optimization and delivery for lightning-fast loading
*   **Mobile-First Design:** Every theme is fully responsive and optimized for all devices
*   **Modern Dashboard:** Intuitive portfolio management with preview, edit, and export capabilities

---

## 🚀 Built With

This project leverages cutting-edge technologies to deliver a premium, scalable experience.

### **Core Technologies:**
*   **Framework:** [Next.js 15](https://nextjs.org/) with App Router for optimal performance
*   **Styling:** [Tailwind CSS v4](https://tailwindcss.com/) & [Shadcn/UI](https://ui.shadcn.com/) for modern design
*   **Database:** [Firestore](https://firebase.google.com/docs/firestore) for real-time data management
*   **Authentication:** [Firebase Authentication](https://firebase.google.com/docs/auth) with Google OAuth
*   **Media Storage:** [Cloudinary](https://cloudinary.com/) for optimized image delivery
*   **Deployment:** [Vercel](https://vercel.com/) for global edge deployment

### **State Management:**
*   **Server State:** [TanStack Query](https://tanstack.com/query/latest) for data fetching and caching
*   **Editor State:** React Context with `useReducer` for portfolio editing
*   **Global State:** [Zustand](https://zustand-demo.pmnd.rs/) for authentication and UI state

### **Export System:**
*   **Live DOM Capture:** Revolutionary client-side HTML capture technology
*   **Static Generation:** Complete, self-contained website packages
*   **Universal Compatibility:** Works with any static hosting service

## 🏗️ Project Architecture

### **Current Implementation Status**

**✅ Production Ready:**
- **Live DOM Capture Export** (`lib/live-dom-capture.ts`) - Primary export system
- **Theme Registry System** (`themes/theme-registry.ts`) - Automatic theme management
- **Firebase Integration** - Authentication and data storage
- **Cloudinary Integration** - Optimized image handling
- **Modern & Creative Themes** - Two professional themes available

**⚠️ Legacy/Fallback Systems:**
- **Client Export** (`lib/client-export.ts`) - Fallback export system for edge cases
- **API Sync Route** (`app/api/sync-themes/route.ts`) - Used during publishing process

**🗂️ Key Directories:**
```
profolify/
├── app/                          # Next.js 15 App Router
│   ├── (private)/               # Protected routes (dashboard, editor)
│   ├── (public)/                # Public routes (landing, portfolios)
│   └── api/                     # API routes (theme sync)
├── components/                   # React components
│   ├── portfolio-themes/        # Theme-specific components
│   └── ui/                      # Reusable UI components
├── contexts/                    # React contexts (auth, editor, export)
├── hooks/                       # Custom React hooks
├── lib/                         # Core utilities and APIs
├── themes/                      # Theme system (CSS + components)
├── public/themes/               # Synced CSS files (auto-generated)
└── scripts/                     # Build and theme management scripts
```

---

## 🏗️ Architectural Deep Dive

Profolify is built on a foundation of modern software design patterns to ensure it is robust, scalable, and maintainable.

### 1. The Portfolio Editor: A "Smart Manager" Architecture

The core of the application is the portfolio editor. To handle its complex state without creating bugs or messy code, we implemented a centralized state management pattern.

*   **The Problem:** A portfolio has dozens of fields. Managing this with simple `useState` hooks would lead to "prop drilling" and make the code difficult to maintain.
*   **The Solution:** We use a combination of **React Context** and the **`useReducer` hook**.
    *   **`EditorContext.tsx`** creates a central "store" for the editor's state.
    *   A `reducer` function defines all possible state changes (e.g., `UPDATE_FIELD`, `ADD_PROJECT`). This ensures all updates are predictable and flow through one central point.
    *   The main editor page (`/portfolio`) acts as a "smart container," managing API calls and providing the context.
    *   Individual components (`HeroSection`, etc.) are "dumb" presentational components. They receive data as props and report changes back up by calling functions passed down as props (e.g., `onFieldUpdate`).
    *   The editor header is now a dedicated `EditorHeader` component, featuring a modern sticky design, grouped action buttons, and a user avatar dropdown for navigation and account actions.

This pattern eliminates prop drilling, makes debugging straightforward, and allows us to easily add new editable sections in the future without refactoring existing logic.

### 2. Revolutionary Export Architecture: Live DOM Capture

Profolify's export system represents a breakthrough in portfolio technology, solving complex challenges that traditional template-based systems couldn't handle.

*   **The Challenge:** Traditional portfolio builders use static templates that break unique theme layouts and require separate HTML generation for each theme, creating maintenance nightmares.
*   **Our Innovation:** Live DOM Capture technology that captures the actual rendered HTML from your live portfolio, preserving every detail and working with any theme automatically.
*   **The Result:** Pixel-perfect exports that match your live site exactly, with zero maintenance overhead for new themes and sub-second export performance.

## 🚀 Export Functionality - Live DOM Capture System

### Revolutionary Approach: Live DOM Capture
Profolify uses a groundbreaking **Live DOM Capture Export System** that captures the actual rendered DOM from your live portfolio, ensuring pixel-perfect exports that match your live site exactly.

**How It Works:**
1. **Smart Context Detection**: Automatically detects if you're on the portfolio page or dashboard
2. **Live DOM Capture**: Captures the actual rendered HTML from your live portfolio
3. **Multi-Layer Image Fixing**: Converts Next.js optimized images to direct URLs for static compatibility
4. **Mobile Menu Enhancement**: Adds native JavaScript functionality for exported sites
5. **CSS Reset Integration**: Ensures full-width layouts without browser default margins
6. **ZIP Generation**: Creates a complete, self-contained static website

### Technical Excellence

**🎯 Live DOM Capture Process:**
```typescript
// Context-aware capture strategy
const isOnPortfolioPage = currentPath.includes('/portfolio') || currentPath === `/${portfolioData.slug}`;

if (isOnPortfolioPage) {
  // Direct DOM capture from current page
  const themeRoot = document.querySelector('[class*="theme-"][class*="-root"]');
  capturedHTML = themeRoot.outerHTML;
} else {
  // Hidden iframe capture from live portfolio URL
  const iframe = document.createElement('iframe');
  iframe.src = `${window.location.origin}/${portfolioData.slug}`;
  // ... capture from iframe when loaded
}
```

**🖼️ Advanced Image Processing (6-Layer Defense):**
- **Layer 1**: Enhanced export context detection
- **Layer 2**: PortfolioImage component URL extraction
- **Layer 3**: DOM-based image fixing
- **Layer 4**: HTML string-based fixing with multiple regex patterns
- **Layer 5**: Aggressive final fix for any remaining Next.js URLs
- **Layer 6**: Final verification and force-fix

**📱 Native Mobile Menu System:**
```javascript
// Native JavaScript for exported sites
document.addEventListener('DOMContentLoaded', function() {
  const toggles = document.querySelectorAll('[data-mobile-menu-toggle]');
  toggles.forEach(toggle => {
    toggle.addEventListener('click', function() {
      const menu = document.getElementById(toggle.dataset.target);
      menu.style.display = menu.style.display === 'none' ? 'block' : 'none';
    });
  });
});
```

### Implementation Journey

**❌ Template-Based Approach (Abandoned):**
- Required separate HTML generation functions for each theme
- Broke unique theme layouts by forcing identical structure
- Maintenance nightmare with theme-specific templates
- Scalability issues when adding new themes

**✅ Live DOM Capture Solution (Current):**
- **Theme Agnostic**: Works with any theme automatically
- **Perfect Fidelity**: Exported sites match live sites exactly
- **Client-Side Only**: No server dependencies or timeouts
- **Future Proof**: New themes work without code changes
- **Sub-Second Performance**: Instant export generation

### Export Quality Achievements

**✅ Perfect Image Loading:**
- Transforms `/_next/image?url=...` → Direct Cloudinary URLs
- All hero, project, and about images load correctly
- Automatic fallback to placeholders for failed images

**✅ Full-Width Layouts:**
- Comprehensive CSS reset removes browser default 8px margin
- Exported sites take full browser width like live sites
- Professional appearance across all devices

**✅ Mobile Responsiveness:**
- Native hamburger menu functionality
- Touch-friendly navigation
- Perfect responsive design preservation

**✅ Production Ready:**
- Self-contained HTML files
- No external dependencies except optional CDN
- Works on any static hosting service
- SEO-optimized with proper meta tags

---

## 🚀 Getting Started

To get a local copy up and running, follow these simple steps.

### Prerequisites

*   Node.js (v18 or newer)
*   npm
*   A Firebase project
*   A Cloudinary account

### Installation

1.  **Clone the repo:**
    ```sh
    git clone https://github.com/your-username/profolify.git
    ```
2.  **Install NPM packages:**
    ```sh
    npm install
    ```
3.  **Set up your environment variables:**
    *   Create a `.env.local` file in the root of the project.
    *   Populate it with your Firebase and Cloudinary API keys.
    ```ini
    # Firebase Configuration
    NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
    NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
    NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
    NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
    NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
    NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

    # Cloudinary Configuration
    NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloud_name
    CLOUDINARY_API_KEY=your_api_key
    CLOUDINARY_API_SECRET=your_api_secret
    ```
4.  **Sync theme CSS files:**
    ```sh
    npm run sync-themes
    ```
5.  **Run the development server:**
    ```sh
    npm run dev
    ```
    Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

### Available Scripts

*   **`npm run dev`** - Start development server with Turbopack
*   **`npm run build`** - Build for production (includes CSS compilation and theme sync)
*   **`npm run start`** - Start production server
*   **`npm run compile-css`** - Compile modular CSS files into single files for export
*   **`npm run sync-themes`** - Compile CSS and sync theme files to public directory
*   **`npm run validate-themes`** - Validate theme structure without syncing
*   **`npm run create-theme`** - Generate new theme boilerplate

## 🎯 Current Features & Status

### **✅ Fully Implemented**
- **User Authentication** - Google OAuth with Firebase
- **Portfolio Editor** - Real-time WYSIWYG editing experience
- **Theme System** - Modern and Creative Minimalist themes
- **Image Management** - Cloudinary integration with optimization
- **Live DOM Capture Export** - Revolutionary static site generation
- **Mobile Responsive** - Perfect experience on all devices
- **SEO Optimization** - Meta tags, Open Graph, Twitter Cards
- **Custom URLs** - Personalized portfolio links

### **🔧 Technical Achievements**
- **6-Layer Image Fixing** - Ensures perfect image loading in exports
- **Native Mobile Menus** - JavaScript-based navigation for static sites
- **CSS Reset Integration** - Full-width layouts without browser defaults
- **Dual CSS Architecture** - Modular CSS for development, compiled CSS for exports
- **Smart Context Detection** - Adaptive export methods
- **Theme Registry System** - Automatic CSS compilation and syncing

### **🚀 Future Enhancements**
- **Additional Themes** - More professional templates
- **Advanced Customization** - Color schemes and typography options
- **Analytics Integration** - Portfolio performance tracking
- **Custom Domains** - Personal domain support
- **Team Collaboration** - Multi-user portfolio management

---

