import type { Metadata } from "next";
import "./globals.css"; // Your globals.css should be imported here
// Import theme CSS files
import "../themes/modern/modern.css";
import "../themes/creative-minimalist/creative-minimalist-modular.css";
import { Toaster } from "@/components/ui/sonner";
import { ThemeProvider } from "@/contexts/theme-provider";
import { GeistSans } from "geist/font/sans"; // Import the sans-serif font
import { GeistMono } from "geist/font/mono"; // Import the monospaced font
import { AuthProvider } from "@/contexts/auth-provider";
import { QueryProvider } from "@/contexts/query-provider";


export const metadata: Metadata = {
  title: "Profolify - Build Your Professional Portfolio",
  description: "Create and share your professional portfolio and resume with ease.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${GeistSans.variable} ${GeistMono.variable}`} suppressHydrationWarning>
      <body >
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem={false}
          disableTransitionOnChange
        >
          <AuthProvider>
            <QueryProvider>
              {children}
              <Toaster />
            </QueryProvider>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}