import { getPortfolioBySlug } from '@/lib/portfolio-api';
import { notFound } from 'next/navigation';
import { Metadata } from 'next';

// --- NEW: Import theme registry ---
import { getThemeComponent } from '@/themes/theme-registry';

// Generate dynamic metadata for each portfolio
export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }): Promise<Metadata> {
    const { slug } = await params;
    const portfolioData = await getPortfolioBySlug(slug);

    if (!portfolioData) {
        return {
            title: 'Portfolio Not Found',
            description: 'The requested portfolio could not be found.',
        };
    }

    const title = portfolioData.userName ? `${portfolioData.userName} - Portfolio` : 'Portfolio';
    const description = portfolioData.about
        ? portfolioData.about.substring(0, 160) + (portfolioData.about.length > 160 ? '...' : '')
        : `${portfolioData.userName || 'Professional'} - ${portfolioData.profession || 'Portfolio'}`;

    return {
        title,
        description,
        openGraph: {
            title,
            description,
            type: 'profile',
            url: `/${slug}`,
        },
        twitter: {
            card: 'summary',
            title,
            description,
        },
    };
}

export default async function PublicPortfolioPage({ params }: { params: Promise<{ slug: string }> }) {
    // 1. Fetch the data (no change here)
    const { slug } = await params;
    const portfolioData = await getPortfolioBySlug(slug);

    // 2. Handle the case where the portfolio is not found or not published
    if (!portfolioData) {
        notFound();
    }

    // 3. --- Use theme registry to render the correct theme ---
    const ThemeComponent = getThemeComponent(portfolioData.templateId);
    if (!ThemeComponent) {
        // Fallback to default theme if not found
        const DefaultComponent = getThemeComponent('creative-theme-v1');
        return DefaultComponent ?
            <DefaultComponent isEditing={false} serverData={portfolioData} /> :
            <div>Theme not found</div>;
    }

    return <ThemeComponent isEditing={false} serverData={portfolioData} />;
}