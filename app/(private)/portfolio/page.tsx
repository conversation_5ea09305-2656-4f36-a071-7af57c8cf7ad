"use client";

import { useEffect, useState, useRef } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/stores/auth-store";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useDebounce } from "@/hooks/use-debounce";
import { useThemeSync } from "@/hooks/useThemeSync";
import { PortfolioData } from "@/lib/types";
import { getPortfolio, updatePortfolio, deletePortfolio, uploadFile, generateUniqueSlug } from "@/lib/portfolio-api";
import { EditorProvider, useEditor } from "@/contexts/EditorContext";
import { isEqual } from 'lodash';

// UI and Theme Components
import { getThemeComponent } from "@/themes/theme-registry";

import { Loader2 } from "lucide-react";
import { ConfirmationDialog } from "@/components/ui/ConfirmationDialog";
import { EditorHeader } from "./components/EditorHeader";
import { ExportProvider } from "@/contexts/ExportContext";
import PortfolioLoading from "./loading";

function PortfolioEditorCore() {
  const { user } = useAuthStore();
  const queryClient = useQueryClient();
  const router = useRouter();
  const { state, dispatch } = useEditor();
  const { formData } = state;

  // Ensure theme CSS files are synced in development
  useThemeSync();

  const debouncedFormData = useDebounce(formData, 1500);
  const serverStateRef = useRef<PortfolioData | null>(null);
  const [isDirty, setIsDirty] = useState(false);

  useEffect(() => {
    if (formData && !serverStateRef.current) {
      serverStateRef.current = formData;
    }
  }, [formData]);

  useEffect(() => {
    if (serverStateRef.current) {
      const hasChanges = !isEqual(formData, serverStateRef.current);
      setIsDirty(hasChanges);
    }
  }, [formData]);

  // --- MUTATIONS ---

  const { mutate: autoSave } = useMutation({
    mutationFn: (data: PortfolioData) => {
      if (!user) throw new Error('User not authenticated');
      return updatePortfolio({ userId: user.uid, data });
    },
    onSuccess: () => {
      // Corrected: The ONLY job of a successful auto-save is to show the status.
      // It does NOT change isDirty or serverStateRef. This keeps the "Update Live"
      // button correctly enabled because the changes are saved but not yet published.
      dispatch({ type: 'SET_SAVE_STATUS', payload: 'saved' });
    },
    onError: () => dispatch({ type: 'SET_SAVE_STATUS', payload: 'error' }),
  });

  const { mutate: publishMutation, isPending: isPublishing } = useMutation({
    mutationFn: async (data: PortfolioData): Promise<PortfolioData> => {
      if (!user) throw new Error('User not authenticated');

      // Ensure theme CSS is synced before publishing
      toast.info("Preparing your portfolio for publishing...");

      try {
        // Sync theme CSS files
        const response = await fetch('/api/sync-themes', { method: 'POST' });
        if (!response.ok) {
          console.warn('Theme sync failed, but continuing with publish');
        } else {
          console.log('✅ Theme CSS synced successfully');
        }
      } catch (error) {
        console.warn('Theme sync failed, but continuing with publish:', error);
      }

      const dataToSave = { ...data, isPublished: true };
      const hasNameChanged = serverStateRef.current?.userName !== data.userName;
      const isDefaultSlug = data.slug === user.uid;
      const needsNewUrl = (hasNameChanged || isDefaultSlug);

      if (needsNewUrl && data.userName) {
        toast.info("Generating your new public URL...");
        const newSlug = await generateUniqueSlug(data.userName, user.uid);
        dataToSave.slug = newSlug;
      }

      await updatePortfolio({ userId: user.uid, data: dataToSave });
      return dataToSave;
    },
    onSuccess: (savedData) => {
      if (!user) return;

      queryClient.invalidateQueries({ queryKey: ['portfolio', user.uid] });

      // Correct: Publishing is the ONLY action that makes the state "clean".
      serverStateRef.current = savedData;
      setIsDirty(false);
      toast.success("Portfolio published successfully!");
      router.push('/dashboard');
    },
    onError: (error) => {
      console.error("Publish failed:", error);
      toast.error("Failed to publish portfolio. Please try again.");
    },
  });

  const { mutate: deleteMutation, isPending: isDeleting } = useMutation({
    mutationFn: () => {
      if (!user) throw new Error('User not authenticated');
      return deletePortfolio(user.uid);
    },
    onSuccess: () => {
      if (!user) return;
      queryClient.removeQueries({ queryKey: ['portfolio', user.uid] });
      toast.success("Portfolio discarded. You can choose a new template to start over.");
      router.push('/dashboard');
    },
    onError: () => {
      toast.error("Failed to delete portfolio.");
    },
  });

  const { mutate: uploadMutation } = useMutation({
    mutationFn: (vars: { file: File, type: 'profile' | 'resume' | 'project', id?: string }) => {
      console.log('🚀 Starting upload:', { type: vars.type, id: vars.id, fileName: vars.file.name });
      dispatch({ type: 'SET_UPLOADING', payload: { type: vars.type, id: vars.id } });
      return uploadFile(vars.file);
    },
    onSuccess: (url, vars) => {
      console.log('✅ Upload successful:', { type: vars.type, id: vars.id, url });

      // Add timestamp to force re-render and cache busting
      const urlWithTimestamp = url.includes('?') ? `${url}&t=${Date.now()}` : `${url}?t=${Date.now()}`;

      toast.success(`${vars.type.charAt(0).toUpperCase() + vars.type.slice(1)} uploaded successfully!`);

      if (vars.type === 'profile') {
        dispatch({ type: 'UPDATE_FIELD', payload: { field: 'profileImageUrl', value: urlWithTimestamp } });
      } else if (vars.type === 'resume') {
        dispatch({ type: 'UPDATE_FIELD', payload: { field: 'resumeUrl', value: url } });
      } else if (vars.type === 'project' && vars.id) {
        console.log('🔍 Looking for project with ID:', vars.id);
        console.log('📋 Current projects:', formData.projects.map(p => ({ id: p.id, title: p.title })));

        const projectIndex = formData.projects.findIndex(p => p.id === vars.id);
        console.log('📍 Found project at index:', projectIndex);

        if (projectIndex !== -1) {
          console.log('🔄 Updating project image at index:', projectIndex);
          dispatch({ type: 'UPDATE_PROJECT', payload: { index: projectIndex, field: 'imageUrl', value: urlWithTimestamp } });
        } else {
          console.error('❌ Project not found with ID:', vars.id);
          toast.error("Project not found. Please try again.");
        }
      }
    },
    onError: (error) => {
      console.error('❌ Upload error:', error);
      toast.error("Upload failed. Please try again.");
    },
    onSettled: () => {
      console.log('🏁 Upload settled, clearing upload state');
      dispatch({ type: 'SET_UPLOADING', payload: null });
    },
  });

  // --- Auto-save effect ---
  useEffect(() => {
    // The auto-save draft functionality.
    if (isDirty) {
      dispatch({ type: 'SET_SAVE_STATUS', payload: 'saving' });
      autoSave(debouncedFormData);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debouncedFormData, autoSave, isDirty]);

  // --- Handlers & State ---
  const [dialog, setDialog] = useState({ isOpen: false, onConfirm: () => { } });
  const handleDeleteConfirm = () => setDialog({ isOpen: true, onConfirm: () => deleteMutation() });



  // Early return if user is not authenticated (e.g., during build time)
  // This is after all hooks to avoid React hooks rules violation
  if (!user) {
    return <div className="flex h-full items-center justify-center"><Loader2 className="h-8 w-8 animate-spin" /></div>;
  }

  return (
    <>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/30">
        <EditorHeader
          isPublishing={isPublishing}
          isDeleting={isDeleting}
          isDirty={isDirty}
          onDelete={handleDeleteConfirm}
          onTogglePublish={() => publishMutation(formData)}
        />

        <main className="p-2">
          <div className="max-w-[95vw] mx-auto my-4">
            {/* Canvas Container */}
            <div className="bg-white rounded-2xl shadow-2xl border border-slate-200/60 overflow-hidden">
              {/* Canvas Header */}
              <div className="bg-gradient-to-r from-slate-50 to-blue-50/50 px-6 py-4 border-b border-slate-200/60">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="flex gap-2">
                      <div className="w-3 h-3 rounded-full bg-red-400"></div>
                      <div className="w-3 h-3 rounded-full bg-yellow-400"></div>
                      <div className="w-3 h-3 rounded-full bg-green-400"></div>
                    </div>
                    <span className="text-sm font-medium text-slate-600">Portfolio Editor Canvas</span>
                  </div>
                  {/* Editor Tips */}
                  <div className="text-center">
                    <p className="text-sm text-slate-500">
                      💡 <strong>Tip:</strong> Click on any editable section below to edit your portfolio content.
                    </p>
                  </div>


                </div>
              </div>

              {/* Canvas Content */}
              <div className="relative bg-white overflow-hidden">
                <div className="absolute inset-0 bg-grid-pattern opacity-5 pointer-events-none"></div>

                {/* Theme Container - Isolated viewport */}
                <div className="relative portfolio-theme-container">
                  {(() => {
                    const ThemeComponent = getThemeComponent(formData.templateId);
                    if (!ThemeComponent) {
                      return (
                        <div className="flex h-96 items-center justify-center">
                          <div className="text-center">
                            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-slate-100 flex items-center justify-center">
                              <span className="text-2xl">⚠️</span>
                            </div>
                            <p className="text-slate-600 font-medium">Theme not found</p>
                            <p className="text-slate-400 text-sm">{formData.templateId}</p>
                          </div>
                        </div>
                      );
                    }
                    return <ThemeComponent isEditing={true} onImageUpload={uploadMutation} />;
                  })()}
                </div>
              </div>
            </div>


          </div>
        </main>
      </div>
      <ConfirmationDialog
        isOpen={dialog.isOpen}
        onClose={() => setDialog({ isOpen: false, onConfirm: () => { } })}
        onConfirm={() => { dialog.onConfirm(); setDialog({ isOpen: false, onConfirm: () => { } }); }}
        title="Are you absolutely sure?"
        description="This action is irreversible and will permanently delete all of your portfolio data."
      />
    </>
  );
}

// The page wrapper that fetches data and provides the context
export default function PortfolioPage() {
  const { user } = useAuthStore();
  const router = useRouter();

  const { data: initialData, isLoading } = useQuery<PortfolioData | null>({
    queryKey: ["portfolio", user?.uid],
    queryFn: () => getPortfolio(user!.uid),
    enabled: !!user,
    staleTime: 5 * 60 * 1000,
  });

  useEffect(() => {
    // Wait for the query to finish and only redirect if a user is loaded but has no portfolio data.
    if (!isLoading && user && !initialData) {
      toast.error("No portfolio found. Please select a template to begin.");
      router.replace('/dashboard');
    }
  }, [isLoading, initialData, router, user]);

  // Show a loader while authenticating or fetching data.
  if (isLoading || !user) {
    return <PortfolioLoading />;
  }

  // If loading is complete but there's no data, it means we're about to redirect.
  // Show a loader to prevent rendering the editor with null data.
  if (!initialData) {
    return <PortfolioLoading />;
  }

  return (
    <ExportProvider value={false}>
      <EditorProvider initialData={initialData}>
        <PortfolioEditorCore />
      </EditorProvider>
    </ExportProvider>
  );
}