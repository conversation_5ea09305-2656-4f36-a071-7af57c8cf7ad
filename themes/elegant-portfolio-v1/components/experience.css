.theme-elegant-portfolio-experience {
  padding: 5rem 0;
  background: #f9fafb;
}
.theme-elegant-portfolio-experience-title {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
}
.theme-elegant-portfolio-experience-list {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  max-width: 900px;
  margin: 0 auto;
}
.theme-elegant-portfolio-experience-item {
  background: #fff;
  border-radius: 0.75rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 2rem;
  transition: box-shadow 0.2s;
}
.theme-elegant-portfolio-experience-item:hover {
  box-shadow: 0 4px 16px rgba(59,130,246,0.08);
}
@media (max-width: 768px) {
  .theme-elegant-portfolio-experience-list {
    gap: 1rem;
  }
  .theme-elegant-portfolio-experience-item {
    padding: 1rem;
  }
}
