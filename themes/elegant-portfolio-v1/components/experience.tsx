import React from 'react';
import { SectionProps } from '@/lib/types';

export function ElegantPortfolioExperience({ isEditing, serverData }: SectionProps) {
  const data = serverData?.experience || [
    { title: 'Frontend Developer', company: 'Tech Corp', period: '2022 - Present', description: 'Building modern UIs.' },
    { title: 'Web Designer', company: 'Design Studio', period: '2020 - 2022', description: 'Crafted beautiful websites.' },
  ];
  return (
    <section className="theme-elegant-portfolio-experience">
      <h2 className="theme-elegant-portfolio-experience-title">Experience</h2>
      <div className="theme-elegant-portfolio-experience-list">
        {data.map((item: any, idx: number) => (
          <div className="theme-elegant-portfolio-experience-item" key={idx}>
            <h3>{item.title}</h3>
            <div><strong>{item.company}</strong> &mdash; {item.period}</div>
            <p>{item.description}</p>
          </div>
        ))}
      </div>
    </section>
  );
}
