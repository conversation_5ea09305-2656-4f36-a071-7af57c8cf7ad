import React, { useState } from 'react';
import { SectionProps } from '@/lib/types';

export function ElegantPortfolioNavbar({ serverData }: SectionProps) {
  const [mobileOpen, setMobileOpen] = useState(false);
  const links = [
    { label: 'Home', href: '#' },
    { label: 'About', href: '#about' },
    { label: 'Experience', href: '#experience' },
    { label: 'Contact', href: '#footer' },
  ];
  return (
    <nav className="theme-elegant-portfolio-navbar">
      <div className="theme-elegant-portfolio-navbar-brand">Elegant Portfolio</div>
      <button
        className="theme-elegant-portfolio-navbar-mobile-btn"
        aria-label="Toggle menu"
        onClick={() => setMobileOpen((v) => !v)}
      >
        <span>&#9776;</span>
      </button>
      <div className={`theme-elegant-portfolio-navbar-links${mobileOpen ? ' active' : ''}`}>
        {links.map(link => (
          <a key={link.href} href={link.href} className="theme-elegant-portfolio-navbar-link">
            {link.label}
          </a>
        ))}
      </div>
    </nav>
  );
}
