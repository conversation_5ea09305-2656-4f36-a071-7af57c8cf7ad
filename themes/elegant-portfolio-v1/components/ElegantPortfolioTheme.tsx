import React from 'react';
import { ProfolifyThemeProps } from '@/lib/types';
import { ElegantPortfolioNavbar } from './ElegantPortfolioNavbar';
import { ElegantPortfolioHero } from './ElegantPortfolioHero';
import { ElegantPortfolioAbout } from './about';
import { ElegantPortfolioExperience } from './experience';
import { ElegantPortfolioFooter } from './footer';

export function ElegantPortfolioTheme({ isEditing, serverData, onImageUpload }: ProfolifyThemeProps) {
  return (
    <div className="theme-elegant-portfolio-root">
      <ElegantPortfolioNavbar isEditing={isEditing} serverData={serverData} />
      <ElegantPortfolioHero isEditing={isEditing} serverData={serverData} onImageUpload={onImageUpload} />
      <ElegantPortfolioAbout isEditing={isEditing} serverData={serverData} />
      <ElegantPortfolioExperience isEditing={isEditing} serverData={serverData} />
      <ElegantPortfolioFooter />
    </div>
  );
}
