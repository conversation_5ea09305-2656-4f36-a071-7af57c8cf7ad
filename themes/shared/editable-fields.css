/* Shared Editable Field Styles */
/* This file provides consistent editable field styling across all themes */

/* Base editable field styling */
.editable-field {
  position: relative;
  border: 2px dashed rgba(102, 126, 234, 0.3);
  border-radius: 0.375rem;
  padding: 0.5rem;
  background: rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
  min-height: 1.5rem;
  cursor: text;
}

.editable-field:hover {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(102, 126, 234, 0.1);
}

.editable-field:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Placeholder styling for empty editable fields */
.editable-field:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
  pointer-events: none;
}

/* Special styling for gradient text fields (like hero names) */
.editable-field-gradient {
  position: relative;
  border: 2px dashed rgba(102, 126, 234, 0.3);
  border-radius: 0.375rem;
  padding: 0.5rem;
  /* No background - preserve gradient text */
  transition: all 0.3s ease;
  min-height: 1.5rem;
  cursor: text;
}

.editable-field-gradient:hover {
  border-color: rgba(102, 126, 234, 0.5);
  /* Don't add background - preserve gradient text */
}

.editable-field-gradient:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  /* Don't add background - preserve gradient text */
}

.editable-field-gradient:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
  pointer-events: none;
}

/* Inline editable fields (for smaller text elements) */
.editable-field-inline {
  position: relative;
  border: 1px dashed rgba(102, 126, 234, 0.3);
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
  min-height: 1rem;
  cursor: text;
  display: inline-block;
}

.editable-field-inline:hover {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(102, 126, 234, 0.1);
}

.editable-field-inline:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.editable-field-inline:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
  pointer-events: none;
}

/* Large editable fields (for descriptions, content areas) */
.editable-field-large {
  position: relative;
  border: 2px dashed rgba(102, 126, 234, 0.3);
  border-radius: 0.5rem;
  padding: 1rem;
  background: rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
  min-height: 3rem;
  cursor: text;
}

.editable-field-large:hover {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(102, 126, 234, 0.1);
}

.editable-field-large:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.editable-field-large:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
  pointer-events: none;
}

/* Theme-specific color variants */

/* Modern theme variant (blue gradient) */
.editable-field-modern {
  border-color: rgba(59, 130, 246, 0.3);
  background: rgba(59, 130, 246, 0.05);
}

.editable-field-modern:hover {
  border-color: rgba(59, 130, 246, 0.5);
  background: rgba(59, 130, 246, 0.1);
}

.editable-field-modern:focus {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Creative theme variant (purple gradient) */
.editable-field-creative {
  border-color: rgba(102, 126, 234, 0.3);
  background: rgba(102, 126, 234, 0.05);
}

.editable-field-creative:hover {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(102, 126, 234, 0.1);
}

.editable-field-creative:focus {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Utility classes for common combinations */
.editable-title {
  font-size: inherit;
  font-weight: inherit;
  color: inherit;
  line-height: inherit;
}

.editable-text {
  font-size: inherit;
  font-weight: inherit;
  color: inherit;
  line-height: inherit;
}

.editable-description {
  font-size: inherit;
  font-weight: inherit;
  color: inherit;
  line-height: inherit;
  white-space: pre-wrap;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .editable-field,
  .editable-field-large {
    padding: 0.75rem;
  }
  
  .editable-field-inline {
    padding: 0.25rem;
  }
}
