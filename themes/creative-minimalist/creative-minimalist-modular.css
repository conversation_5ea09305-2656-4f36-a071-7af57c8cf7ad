/* Creative Minimalist Theme - Modular CSS */

/* Import Component Styles */
@import url('./components/navbar.css');
@import url('./components/hero.css');
@import url('./components/about.css');
@import url('./components/experience.css');
@import url('./components/skills.css');
@import url('./components/projects.css');
@import url('./components/contact.css');
@import url('./components/footer.css');

/* Base styles and reset */
.theme-creative-root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  line-height: 1.6;
  color: #111827;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  background: white;
  overflow-x: hidden;
}

.theme-creative-root *,
.theme-creative-root *::before,
.theme-creative-root *::after {
  box-sizing: inherit;
}

/* Container utilities */
.theme-creative-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .theme-creative-container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .theme-creative-container {
    padding: 0 2rem;
  }
}

/* Common Button Styles */
.theme-creative-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 1rem;
}

.theme-creative-btn-primary {
  background: #3b82f6;
  color: white;
}

.theme-creative-btn-primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.theme-creative-btn-secondary {
  background: transparent;
  color: #3b82f6;
  border: 2px solid #3b82f6;
}

.theme-creative-btn-secondary:hover {
  background: #3b82f6;
  color: white;
}

.theme-creative-btn-danger {
  background: #ef4444;
  color: white;
}

.theme-creative-btn-danger:hover {
  background: #dc2626;
}

/* Common Form Styles */
.theme-creative-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.theme-creative-input:focus {
  outline: none;
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}

.theme-creative-textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
  resize: vertical;
  min-height: 4rem;
}

.theme-creative-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}

/* Common Card Styles */
.theme-creative-card {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.theme-creative-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-color: #d1d5db;
}

/* Utility Classes */
.theme-creative-text-center {
  text-align: center;
}

.theme-creative-text-left {
  text-align: left;
}

.theme-creative-text-right {
  text-align: right;
}

.theme-creative-hidden {
  display: none;
}

.theme-creative-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Loading Spinner */
.theme-creative-spinner {
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid #f3f4f6;
  border-radius: 50%;
  border-top-color: #3b82f6;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Utilities */
@media (max-width: 479px) {
  .theme-creative-hide-mobile {
    display: none;
  }
}

@media (min-width: 480px) {
  .theme-creative-show-mobile {
    display: none;
  }
}

@media (max-width: 767px) {
  .theme-creative-hide-tablet {
    display: none;
  }
}

@media (min-width: 768px) {
  .theme-creative-show-tablet {
    display: none;
  }
}

@media (max-width: 1023px) {
  .theme-creative-hide-desktop {
    display: none;
  }
}

@media (min-width: 1024px) {
  .theme-creative-show-desktop {
    display: none;
  }
}

/* Print Styles */
@media print {
  .theme-creative-no-print {
    display: none;
  }

  .theme-creative-root {
    background: white;
    color: black;
  }

  .theme-creative-card {
    box-shadow: none;
    border: 1px solid #e5e7eb;
    break-inside: avoid;
    margin-bottom: 1rem;
  }
}

/* Accessibility Improvements */
.theme-creative-focus-visible:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Selection Styles */
::selection {
  background: rgba(59, 130, 246, 0.2);
  color: #1f2937;
}

::-moz-selection {
  background: rgba(59, 130, 246, 0.2);
  color: #1f2937;
}
