"use client";
import { ProfolifyThemeProps } from "@/lib/types";
import { CreativeNavbar } from "./CreativeNavbar";
import { CreativeHero } from "./CreativeHero";
import { CreativeAbout } from "./CreativeAbout";
import { CreativeExperience } from "./CreativeExperience";
import { CreativeSkills } from "./CreativeSkills";
import { CreativeProjects } from "./CreativeProjects";
import { CreativeContact } from "./CreativeContact";
import { CreativeFooter } from "./CreativeFooter";

export function CreativeMinimalistTheme({ isEditing, serverData, onImageUpload }: ProfolifyThemeProps) {
    // Helper function to check if section should be shown
    // If not editing and no data, don't show section
    //eslint-disable-next-line @typescript-eslint/no-explicit-any
    const shouldShowSection = (sectionData: any, fallbackCheck?: () => boolean) => {
        if (isEditing) return true; // Always show in editing mode
        if (fallbackCheck) return fallbackCheck();
        return sectionData && Object.values(sectionData).some(value =>
            value !== null && value !== undefined && value !== ''
        );
    };

    // Check if sections have content
    const hasExperience = Boolean(serverData?.experiences && serverData.experiences.length > 0);
    const hasSkills = Boolean(serverData?.skills && serverData.skills.length > 0);
    const hasProjects = Boolean(serverData?.projects && serverData.projects.length > 0);
    const hasContactInfo = Boolean(serverData && (
        serverData.email ||
        serverData.phone ||
        serverData.githubUrl ||
        serverData.linkedinUrl ||
        serverData.twitterUrl
    ));

    return (
        <div className="theme-creative-root">
            <CreativeNavbar isEditing={isEditing} serverData={serverData} />
            <main>
                <CreativeHero isEditing={isEditing} serverData={serverData} onImageUpload={onImageUpload} />
                <CreativeAbout isEditing={isEditing} serverData={serverData} />
                {shouldShowSection(null, () => hasExperience) && (
                    <CreativeExperience isEditing={isEditing} serverData={serverData} />
                )}
                {shouldShowSection(null, () => hasSkills) && (
                    <CreativeSkills isEditing={isEditing} serverData={serverData} />
                )}
                {shouldShowSection(null, () => hasProjects) && (
                    <CreativeProjects isEditing={isEditing} serverData={serverData} onImageUpload={onImageUpload} />
                )}
                {shouldShowSection(null, () => hasContactInfo) && (
                    <CreativeContact isEditing={isEditing} serverData={serverData} />
                )}
            </main>
            <CreativeFooter isEditing={isEditing} serverData={serverData} />
        </div>
    );
};
