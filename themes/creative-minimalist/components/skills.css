/* Modern Skills Section Styles */
.skills-section {
  padding: 4rem 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  position: relative;
  overflow: hidden;
}

.skills-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(99, 102, 241, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

@media (min-width: 768px) {
  .skills-section {
    padding: 6rem 1rem;
  }
}

.skills-container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.skills-header {
  text-align: center;
  margin-bottom: 3rem;
}

@media (min-width: 768px) {
  .skills-header {
    margin-bottom: 4rem;
  }
}

.skills-title {
  font-size: 2.25rem;
  font-weight: 800;
  color: #1e293b;
  margin-bottom: 1rem;
  position: relative;
  background: linear-gradient(135deg, #1e293b 0%, #3b82f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

@media (min-width: 480px) {
  .skills-title {
    font-size: 2.75rem;
  }
}

@media (min-width: 640px) {
  .skills-title {
    font-size: 3.25rem;
  }
}

.skills-title::after {
  content: '';
  position: absolute;
  bottom: -0.75rem;
  left: 50%;
  transform: translateX(-50%);
  width: 4rem;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #6366f1);
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

@media (min-width: 480px) {
  .skills-title::after {
    width: 5rem;
  }
}

.skills-subtitle {
  font-size: 1.125rem;
  color: #64748b;
  font-weight: 500;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

@media (min-width: 768px) {
  .skills-subtitle {
    font-size: 1.25rem;
  }
}

.skills-content {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.skills-category {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.skills-category:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);
}

@media (min-width: 768px) {
  .skills-category {
    padding: 2.5rem;
  }
}

.skills-category-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid rgba(226, 232, 240, 0.5);
  position: relative;
}

.skills-category-header::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 4rem;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #6366f1);
  border-radius: 1px;
}

.skills-category-icon {
  width: 1.5rem;
  height: 1.5rem;
  transition: all 0.3s ease;
}

@media (min-width: 768px) {
  .skills-category-icon {
    width: 1.75rem;
    height: 1.75rem;
  }
}

.skills-category-title {
  font-size: 1.375rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  letter-spacing: -0.025em;
}

@media (min-width: 768px) {
  .skills-category-title {
    font-size: 1.5rem;
  }
}

/* Skills Grid */
.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.25rem;
}

@media (min-width: 768px) {
  .skills-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .skills-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 1.75rem;
  }
}

/* Skill Items */
.skills-item {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid transparent;
  border-radius: 1rem;
  padding: 1.25rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.skills-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #6366f1);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.skills-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
  border-color: rgba(59, 130, 246, 0.2);
}

.skills-item:hover::before {
  opacity: 1;
}

@media (min-width: 768px) {
  .skills-item {
    padding: 1.5rem;
    border-radius: 1.25rem;
  }
}

.skills-item-content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 2rem;
}

.skills-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.95rem;
  text-align: center;
  line-height: 1.4;
  letter-spacing: -0.01em;
}

@media (min-width: 768px) {
  .skills-name {
    font-size: 1rem;
  }
}

.skills-controls {
  margin-top: 1.25rem;
  padding-top: 1.25rem;
  border-top: 1px solid rgba(226, 232, 240, 0.6);
}

.skills-control-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Form Elements */
.skills-input {
  width: 100%;
  border: 2px dashed rgba(59, 130, 246, 0.3);
  border-radius: 0.5rem;
  padding: 0.75rem;
  background: rgba(59, 130, 246, 0.05);
  transition: all 0.3s ease;
  font-family: inherit;
  font-size: 0.9rem;
  font-weight: 500;
  color: #1e293b;
  text-align: center;
}

.skills-input:hover {
  border-color: rgba(59, 130, 246, 0.5);
  background: rgba(59, 130, 246, 0.08);
}

.skills-input:focus {
  outline: none;
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.skills-input::placeholder {
  color: #64748b;
  font-weight: 400;
}

.skills-label {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: #475569;
}

.skills-select {
  width: 100%;
  padding: 0.625rem 2rem 0.625rem 0.625rem;
  border: 2px solid #e2e8f0;
  border-radius: 0.5rem;
  background-color: white;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 500;
  transition: border-color 0.3s ease, background-color 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem;
}

.skills-select:hover {
  border-color: #cbd5e1;
}

.skills-select:focus {
  outline: none;
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.skills-delete-btn {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.2);
}

.skills-delete-btn:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.skills-delete-icon {
  width: 0.875rem;
  height: 0.875rem;
}

.skills-add-container {
  text-align: center;
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(226, 232, 240, 0.5);
}

.skills-add-btn {
  background: linear-gradient(135deg, #3b82f6, #6366f1);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 1rem;
  font-weight: 700;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.2);
  letter-spacing: -0.01em;
}

.skills-add-btn:hover {
  background: linear-gradient(135deg, #2563eb, #4f46e5);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
}

.skills-add-icon {
  width: 1.25rem;
  height: 1.25rem;
}

/* Category-specific colors */
.skills-category-web .skills-item::before { background: linear-gradient(90deg, #3b82f6, #1d4ed8); }
.skills-category-web .skills-category-icon { color: #3b82f6; }
.skills-category-web .skills-item { border-color: rgba(59, 130, 246, 0.1); }

.skills-category-mobile .skills-item::before { background: linear-gradient(90deg, #10b981, #047857); }
.skills-category-mobile .skills-category-icon { color: #10b981; }
.skills-category-mobile .skills-item { border-color: rgba(16, 185, 129, 0.1); }

.skills-category-design .skills-item::before { background: linear-gradient(90deg, #8b5cf6, #7c3aed); }
.skills-category-design .skills-category-icon { color: #8b5cf6; }
.skills-category-design .skills-item { border-color: rgba(139, 92, 246, 0.1); }

.skills-category-data .skills-item::before { background: linear-gradient(90deg, #f59e0b, #d97706); }
.skills-category-data .skills-category-icon { color: #f59e0b; }
.skills-category-data .skills-item { border-color: rgba(245, 158, 11, 0.1); }

.skills-category-devops .skills-item::before { background: linear-gradient(90deg, #6b7280, #4b5563); }
.skills-category-devops .skills-category-icon { color: #6b7280; }
.skills-category-devops .skills-item { border-color: rgba(107, 114, 128, 0.1); }

.skills-category-marketing .skills-item::before { background: linear-gradient(90deg, #ec4899, #db2777); }
.skills-category-marketing .skills-category-icon { color: #ec4899; }
.skills-category-marketing .skills-item { border-color: rgba(236, 72, 153, 0.1); }

.skills-category-business .skills-item::before { background: linear-gradient(90deg, #6366f1, #4f46e5); }
.skills-category-business .skills-category-icon { color: #6366f1; }
.skills-category-business .skills-item { border-color: rgba(99, 102, 241, 0.1); }

.skills-category-other .skills-item::before { background: linear-gradient(90deg, #64748b, #475569); }
.skills-category-other .skills-category-icon { color: #64748b; }
.skills-category-other .skills-item { border-color: rgba(100, 116, 139, 0.1); }

/* Mobile-specific improvements */
@media (max-width: 479px) {
  .skills-section {
    padding: 2.5rem 0.75rem;
  }

  .skills-title {
    font-size: 1.875rem;
  }

  .skills-header {
    margin-bottom: 2rem;
  }

  .skills-content {
    gap: 2rem;
  }

  .skills-category {
    padding: 1.5rem;
    border-radius: 1rem;
  }

  .skills-category-header {
    margin-bottom: 1.5rem;
  }

  .skills-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 1rem;
  }

  .skills-item {
    padding: 1rem;
  }

  .skills-control-group {
    gap: 0.75rem;
  }

  .skills-add-btn {
    padding: 0.875rem 1.5rem;
    font-size: 0.9rem;
  }
}
