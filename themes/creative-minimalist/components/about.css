/* About Section */
.theme-creative-about {
  padding: 3rem 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
}

@media (min-width: 768px) {
  .theme-creative-about {
    padding: 5rem 1rem;
  }
}

.theme-creative-about::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e2e8f0' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.5;
}

.theme-creative-about-container {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.theme-creative-about-header {
  margin-bottom: 3rem;
}

@media (min-width: 768px) {
  .theme-creative-about-header {
    margin-bottom: 4rem;
  }
}

.theme-creative-about-title {
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  text-align: center;
  margin-bottom: 1rem;
}

@media (min-width: 480px) {
  .theme-creative-about-title {
    font-size: 2.5rem;
  }
}

@media (min-width: 640px) {
  .theme-creative-about-title {
    font-size: 3rem;
  }
}

.theme-creative-about-divider {
  width: 4rem;
  height: 4px;
  background: #3b82f6;
  margin: 0 auto;
  border-radius: 2px;
}

@media (min-width: 480px) {
  .theme-creative-about-divider {
    width: 5rem;
  }
}

.theme-creative-about-layout {
  display: grid;
  gap: 2rem;
  align-items: start;
}

@media (min-width: 768px) {
  .theme-creative-about-layout {
    gap: 3rem;
  }
}

@media (min-width: 1024px) {
  .theme-creative-about-layout {
    grid-template-columns: 1.2fr 0.8fr;
    gap: 4rem;
    align-items: start;
  }
}

.theme-creative-about-content {
  display: grid;
  gap: 2rem;
}

@media (min-width: 768px) {
  .theme-creative-about-content {
    gap: 3rem;
  }
}

.theme-creative-about-description,
.theme-creative-about-qualifications {
  background: white;
  padding: 1.5rem;
  border-radius: 1.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  position: relative;
}

@media (min-width: 768px) {
  .theme-creative-about-description,
  .theme-creative-about-qualifications {
    padding: 2rem;
  }
}

@media (min-width: 1024px) {
  .theme-creative-about-description,
  .theme-creative-about-qualifications {
    padding: 2.5rem;
  }
}

.theme-creative-about-description:hover,
.theme-creative-about-qualifications:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-color: #d1d5db;
}

.theme-creative-about-section-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 0.75rem;
}

@media (min-width: 768px) {
  .theme-creative-about-section-title {
    font-size: 1.5rem;
  }
}

.theme-creative-about-section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 2rem;
  height: 2px;
  background: #3b82f6;
}

.theme-creative-about-text,
.theme-creative-about-qualifications-text {
  font-size: 1rem;
  color: #374151;
  line-height: 1.7;
  margin: 0;
}

@media (min-width: 768px) {
  .theme-creative-about-text,
  .theme-creative-about-qualifications-text {
    font-size: 1.125rem;
  }
}

.theme-creative-about-qualifications-text {
  white-space: pre-line;
}

/* Enhanced editable text styling for about section */
.theme-creative-about-text[contenteditable="true"],
.theme-creative-about-qualifications-text[contenteditable="true"] {
  position: relative;
  border: 2px dashed rgba(102, 126, 234, 0.3);
  border-radius: 0.375rem;
  padding: 1rem;
  background: rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
  min-height: 4rem;
}

.theme-creative-about-text[contenteditable="true"]:hover,
.theme-creative-about-qualifications-text[contenteditable="true"]:hover {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(102, 126, 234, 0.1);
}

.theme-creative-about-text[contenteditable="true"]:focus,
.theme-creative-about-qualifications-text[contenteditable="true"]:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.theme-creative-about-text[contenteditable="true"]:empty:before,
.theme-creative-about-qualifications-text[contenteditable="true"]:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
}

.theme-creative-about-highlight {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

/* Mobile-specific improvements */
@media (max-width: 479px) {
  .theme-creative-about {
    padding: 2rem 0.75rem;
  }

  .theme-creative-about-title {
    font-size: 1.75rem;
  }

  .theme-creative-about-header {
    margin-bottom: 2rem;
  }

  .theme-creative-about-layout {
    gap: 1.5rem;
  }

  .theme-creative-about-description,
  .theme-creative-about-qualifications {
    padding: 1.25rem;
    border-radius: 1rem;
  }

  .theme-creative-about-section-title {
    font-size: 1.125rem;
    margin-bottom: 1rem;
  }

  .theme-creative-about-text,
  .theme-creative-about-qualifications-text {
    font-size: 0.95rem;
    line-height: 1.7;
  }
}

/* Tablet improvements */
@media (min-width: 768px) and (max-width: 1023px) {
  .theme-creative-about-layout {
    grid-template-columns: 1fr;
    max-width: 700px;
    margin: 0 auto;
  }
}

/* Print styles for about section */
@media print {
  .theme-creative-about {
    padding: 2rem 0;
    background: white;
  }

  .theme-creative-about-description,
  .theme-creative-about-qualifications {
    background: white;
    box-shadow: none;
    border: 1px solid #e5e7eb;
  }
}
