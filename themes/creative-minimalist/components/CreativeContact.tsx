"use client";
import { useEditorSafe } from "@/contexts/EditorContext";
import { SectionProps } from "@/lib/types";
import { EditableText } from "@/components/ui/EditableText";
import { PortfolioData } from "@/lib/types";
import { Mail, Phone, Github, Linkedin, Twitter } from "lucide-react";

export function CreativeContact({ isEditing, serverData }: SectionProps) {
    const context = useEditorSafe();
    const data = isEditing && context ? context.state.formData : serverData!;
    const dispatch = isEditing && context ? context.dispatch : null;

    const handleUpdate = (field: keyof Omit<PortfolioData, 'projects'>, value: string) => {
        if (dispatch) {
            dispatch({ type: 'UPDATE_FIELD', payload: { field, value } });
        }
    };

    const contactItems = [
        {
            icon: Mail,
            label: "Email",
            value: data.email,
            field: 'email' as keyof Omit<PortfolioData, 'projects'>,
            href: `mailto:${data.email}`,
            color: "contact-email"
        },
        {
            icon: Phone,
            label: "Phone",
            value: data.phone,
            field: 'phone' as keyof Omit<PortfolioData, 'projects'>,
            href: `tel:${data.phone}`,
            color: "contact-phone"
        }
    ];

    const socialLinks = [
        {
            icon: Github,
            url: data.githubUrl,
            field: 'githubUrl' as keyof Omit<PortfolioData, 'projects'>,
            label: "GitHub",
            color: "social-github"
        },
        {
            icon: Linkedin,
            url: data.linkedinUrl,
            field: 'linkedinUrl' as keyof Omit<PortfolioData, 'projects'>,
            label: "LinkedIn",
            color: "social-linkedin"
        },
        {
            icon: Twitter,
            url: data.twitterUrl,
            field: 'twitterUrl' as keyof Omit<PortfolioData, 'projects'>,
            label: "Twitter",
            color: "social-twitter"
        }
    ];

    // Don't render if no contact info and not editing
    const hasContactInfo = data.email || data.phone || socialLinks.some(link => link.url);
    if (!isEditing && !hasContactInfo) {
        return null;
    }

    return (
        <section id="contact" className="contact-section">
            <div className="contact-container">
                <div className="contact-header">
                    <h2 className="contact-title">Let&#39;s Connect</h2>
                    <p className="contact-subtitle">
                        Ready to bring your ideas to life? Let&#39;s discuss your next project and create something amazing together.
                    </p>
                </div>

                <div className="contact-content">
                    {/* Contact Information */}
                    <div className="contact-info">
                        <h3 className="contact-info-title">Get In Touch</h3>
                        <div className="contact-items">
                            {contactItems.map((item) => {
                                const IconComponent = item.icon;
                                if (!item.value && !isEditing) return null;

                                return (
                                    <div key={item.field} className={`contact-item ${item.color}`}>
                                        <div className="contact-item-icon">
                                            <IconComponent className="contact-icon" />
                                        </div>
                                        <div className="contact-item-content">
                                            <span className="contact-label">{item.label}</span>
                                            {isEditing ? (
                                                <EditableText
                                                    isEditing={isEditing}
                                                    tagName="div"
                                                    className="contact-value-editable"
                                                    initialValue={item.value || `Your ${item.label.toLowerCase()}`}
                                                    placeholder={`Enter your ${item.label.toLowerCase()}`}
                                                    onSave={(value) => handleUpdate(item.field, value)}
                                                />
                                            ) : (
                                                item.href ? (
                                                    <a href={item.href} className="contact-value-link">
                                                        {item.value}
                                                    </a>
                                                ) : (
                                                    <span className="contact-value">{item.value}</span>
                                                )
                                            )}
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    </div>

                    {/* Social Links */}
                    <div className="social-section">
                        <h3 className="social-title">Follow Me</h3>
                        <div className="social-grid">
                            {socialLinks.map((social) => {
                                const IconComponent = social.icon;
                                if (!social.url && !isEditing) return null;

                                return (
                                    <div key={social.field} className={`social-item ${social.color}`}>
                                        {isEditing ? (
                                            <div className="social-edit">
                                                <div className="social-icon-wrapper">
                                                    <IconComponent className="social-icon" />
                                                </div>
                                                <div className="social-edit-content">
                                                    <span className="social-label">{social.label}</span>
                                                    <EditableText
                                                        isEditing={isEditing}
                                                        tagName="div"
                                                        className="social-url-editable"
                                                        initialValue={social.url || `Your ${social.label} URL`}
                                                        placeholder={`https://${social.label.toLowerCase()}.com/username`}
                                                        onSave={(value) => handleUpdate(social.field, value)}
                                                    />
                                                </div>
                                            </div>
                                        ) : (
                                            <a
                                                href={social.url}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="social-link"
                                                title={`Follow me on ${social.label}`}
                                            >
                                                <IconComponent className="social-icon" />
                                                <span className="social-label">{social.label}</span>
                                            </a>
                                        )}
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
};
