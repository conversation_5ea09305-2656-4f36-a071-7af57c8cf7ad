/* Modern Contact Section */
.contact-section {
  padding: 5rem 1rem;
  background: linear-gradient(135deg, #f1f5f9 0%, #ffffff 100%);
  position: relative;
  overflow: hidden;
}

.contact-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 25% 75%, rgba(59, 130, 246, 0.04) 0%, transparent 50%),
              radial-gradient(circle at 75% 25%, rgba(99, 102, 241, 0.04) 0%, transparent 50%);
  pointer-events: none;
}

@media (min-width: 768px) {
  .contact-section {
    padding: 6rem 1rem;
  }
}

.contact-container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.contact-header {
  text-align: center;
  margin-bottom: 4rem;
}

@media (min-width: 768px) {
  .contact-header {
    margin-bottom: 5rem;
  }
}

.contact-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1e293b;
  margin-bottom: 1rem;
  position: relative;
  background: linear-gradient(135deg, #1e293b 0%, #3b82f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.02em;
}

@media (min-width: 640px) {
  .contact-title {
    font-size: 3rem;
  }
}

@media (min-width: 768px) {
  .contact-title {
    font-size: 3.5rem;
  }
}

.contact-title::after {
  content: '';
  position: absolute;
  bottom: -0.75rem;
  left: 50%;
  transform: translateX(-50%);
  width: 4rem;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #6366f1);
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

@media (min-width: 480px) {
  .contact-title::after {
    width: 5rem;
  }
}

.contact-subtitle {
  font-size: 1.125rem;
  color: #64748b;
  font-weight: 500;
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
}

@media (min-width: 768px) {
  .contact-subtitle {
    font-size: 1.25rem;
  }
}

.contact-content {
  display: grid;
  gap: 3rem;
}

@media (min-width: 1024px) {
  .contact-content {
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
  }
}

/* Contact Info Section */
.contact-info {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 1.25rem;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(226, 232, 240, 0.8);
}

@media (min-width: 768px) {
  .contact-info {
    padding: 2.5rem;
  }
}

.contact-info-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 0.75rem;
}

.contact-info-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 3rem;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #6366f1);
  border-radius: 1.5px;
}

.contact-items {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.25rem;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.contact-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.08);
}

.contact-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.75rem;
  flex-shrink: 0;
}

.contact-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: white;
}

.contact-item-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.contact-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #64748b;
}

.contact-value {
  font-size: 1rem;
  font-weight: 500;
  color: #1e293b;
  word-break: break-word;
}

.contact-value-link {
  font-size: 1rem;
  font-weight: 500;
  color: #3b82f6;
  text-decoration: none;
  transition: color 0.2s ease;
  word-break: break-word;
}

.contact-value-link:hover {
  color: #2563eb;
  text-decoration: underline;
}

/* Contact Item Colors */
.contact-email .contact-item-icon {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.contact-phone .contact-item-icon {
  background: linear-gradient(135deg, #10b981, #059669);
}

.contact-location .contact-item-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.contact-email {
  border-left-color: #3b82f6;
}

.contact-phone {
  border-left-color: #10b981;
}

.contact-location {
  border-left-color: #f59e0b;
}

/* Social Section */
.social-section {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 1.25rem;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(226, 232, 240, 0.8);
}

@media (min-width: 768px) {
  .social-section {
    padding: 2.5rem;
  }
}

.social-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 0.75rem;
}

.social-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 3rem;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #6366f1);
  border-radius: 1.5px;
}

.social-grid {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.social-item {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.social-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.08);
}

.social-link {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem;
  color: #1e293b;
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-link:hover {
  background: rgba(59, 130, 246, 0.05);
}

.social-edit {
  padding: 1.25rem;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.social-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.75rem;
  flex-shrink: 0;
}

.social-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: white;
}

.social-edit-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
}

.social-label {
  font-weight: 600;
  font-size: 0.875rem;
  color: #64748b;
}

/* Social Item Colors */
.social-github .social-icon-wrapper {
  background: linear-gradient(135deg, #1f2937, #111827);
}

.social-linkedin .social-icon-wrapper {
  background: linear-gradient(135deg, #0077b5, #005885);
}

.social-twitter .social-icon-wrapper {
  background: linear-gradient(135deg, #1da1f2, #0d8bd9);
}

.social-github {
  border-left-color: #1f2937;
}

.social-linkedin {
  border-left-color: #0077b5;
}

.social-twitter {
  border-left-color: #1da1f2;
}

/* Editable Fields */
.contact-value-editable[contenteditable="true"],
.social-url-editable[contenteditable="true"] {
  border: 2px dashed rgba(59, 130, 246, 0.3);
  border-radius: 0.5rem;
  padding: 0.75rem;
  background: rgba(59, 130, 246, 0.05);
  transition: all 0.3s ease;
  min-height: 2rem;
  font-size: 0.95rem;
  color: #1e293b;
  word-break: break-word;
}

.contact-value-editable[contenteditable="true"]:hover,
.social-url-editable[contenteditable="true"]:hover {
  border-color: rgba(59, 130, 246, 0.5);
  background: rgba(59, 130, 246, 0.08);
}

.contact-value-editable[contenteditable="true"]:focus,
.social-url-editable[contenteditable="true"]:focus {
  outline: none;
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.contact-value-editable[contenteditable="true"]:empty:before,
.social-url-editable[contenteditable="true"]:empty:before {
  content: attr(data-placeholder);
  color: #94a3b8;
  font-style: italic;
}

/* Mobile-specific improvements */
@media (max-width: 479px) {
  .contact-section {
    padding: 3rem 0.75rem;
  }

  .contact-title {
    font-size: 2rem;
  }

  .contact-header {
    margin-bottom: 2.5rem;
  }

  .contact-content {
    gap: 2rem;
  }

  .contact-info,
  .social-section {
    padding: 1.5rem;
  }

  .contact-item,
  .social-link,
  .social-edit {
    padding: 1rem;
  }

  .contact-item-icon,
  .social-icon-wrapper {
    width: 2rem;
    height: 2rem;
  }

  .contact-icon,
  .social-icon {
    width: 1rem;
    height: 1rem;
  }
}

/* Print styles */
@media print {
  .contact-section {
    background: white;
  }

  .contact-info,
  .social-section {
    background: white;
    border: 1px solid #e2e8f0;
    box-shadow: none;
  }

  .contact-item,
  .social-item {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #e2e8f0;
  }

  .social-url-editable,
  .contact-value-editable {
    display: none;
  }
}
