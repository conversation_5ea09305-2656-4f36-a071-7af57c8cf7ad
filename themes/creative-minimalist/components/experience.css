/* ===== FRESH EXPERIENCE SECTION DESIGN ===== */

/* Main Section */
.theme-creative-experience {
  padding: 4rem 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
  position: relative;
}

@media (min-width: 768px) {
  .theme-creative-experience {
    padding: 6rem 2rem;
  }
}

/* Container */
.theme-creative-experience-content {
  max-width: 1000px;
  margin: 0 auto;
}

.theme-creative-experience-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1e293b;
  margin-bottom: 0.5rem;
  letter-spacing: -0.025em;
}

@media (min-width: 768px) {
  .theme-creative-experience-title {
    font-size: 3.5rem;
  }
}

.theme-creative-experience-subtitle {
  font-size: 1.125rem;
  color: #64748b;
  font-weight: 400;
  margin-bottom: 2rem;
}

/* Decorative underline */
.theme-creative-experience-title::after {
  content: "";
  display: block;
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  margin: 1rem auto 0;
  border-radius: 2px;
}

/* Timeline Container */
.theme-creative-experience-timeline {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Experience Item */
.theme-creative-experience-item {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.theme-creative-experience-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
}

.theme-creative-experience-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

@media (min-width: 768px) {
  .theme-creative-experience-item {
    padding: 2.5rem;
  }
}

/* Experience Card Content */
.theme-creative-experience-card {
  width: 100%;
}

/* Published Layout */
.theme-creative-experience-published-layout {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 0;
}

.theme-creative-experience-header {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

@media (min-width: 768px) {
  .theme-creative-experience-header {
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
  }
}

.theme-creative-experience-title-section {
  flex: 1;
  min-width: 0;
  gap: 1rem;
}

.theme-creative-experience-role-row {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.theme-creative-experience-icon {
  width: 20px;
  height: 20px;
  color: #3b82f6;
  flex-shrink: 0;
}

.theme-creative-experience-role {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin: 0;
  line-height: 1.3;
}

@media (min-width: 768px) {
  .theme-creative-experience-role {
    font-size: 1.75rem;
  }
}

.theme-creative-experience-company-row {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.theme-creative-experience-company {
  font-size: 1.125rem;
  font-weight: 600;
  color: #3b82f6;
  margin: 0;
}

/* Meta Section (Duration & Location) */
.theme-creative-experience-meta-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: flex-start;
}

@media (min-width: 768px) {
  .theme-creative-experience-meta-section {
    align-items: flex-end;
  }
}

.theme-creative-experience-duration-badge {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.theme-creative-experience-location-badge {
  color: #64748b;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.theme-creative-experience-meta-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

/* Description */
.theme-creative-experience-description {
  color: #475569;
  line-height: 1.6;
  font-size: 1rem;
  margin: 0;
}

/* ===== EDITOR LAYOUT STYLES ===== */

.theme-creative-experience-editor-layout {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Editor Grid Rows */
.theme-creative-experience-editor-row {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .theme-creative-experience-editor-row {
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }
}

/* Editor Fields */
.theme-creative-experience-editor-field {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  min-height: 48px;
}

.theme-creative-experience-field-icon {
  width: 20px;
  height: 20px;
  color: #3b82f6;
  flex-shrink: 0;
}

/* Editable Input Styling */
.theme-creative-experience-field-input {
  flex: 1;
  font-size: 1rem;
  color: #1f2937;
  background: rgba(59, 130, 246, 0.05);
  border: 2px dashed rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  padding: 0.75rem;
  outline: none;
  transition: all 0.3s ease;
  min-height: 24px;
  font-family: inherit;
  line-height: 1.5;
}

.theme-creative-experience-field-input:hover {
  border-color: rgba(59, 130, 246, 0.5);
  background: rgba(59, 130, 246, 0.1);
}

.theme-creative-experience-field-input:focus {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.theme-creative-experience-field-input:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
}

/* Description Field */
.theme-creative-experience-editor-description {
  margin: 0;
}

.theme-creative-experience-editor-description
  .theme-creative-experience-field-input {
  min-height: 60px;
  resize: vertical;
  padding: 1rem;
}

/* Action Buttons */
.theme-creative-experience-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
}

.theme-creative-experience-delete-btn {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.theme-creative-experience-delete-btn:hover {
  background: #dc2626;
  transform: scale(1.05);
}

.theme-creative-experience-delete-icon {
  width: 18px;
  height: 18px;
}

/* Add Experience Button */
.theme-creative-experience-add-container {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

.theme-creative-experience-add-btn {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.theme-creative-experience-add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.theme-creative-experience-add-icon {
  width: 20px;
  height: 20px;
}

/* Empty State */
.theme-creative-experience-empty {
  text-align: center;
  padding: 3rem 1rem;
  color: #6b7280;
}

.theme-creative-experience-empty p {
  font-size: 1.125rem;
  margin-bottom: 2rem;
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 767px) {
  .theme-creative-experience {
    padding: 3rem 1rem;
  }

  .theme-creative-experience-title {
    font-size: 2rem;
  }

  .theme-creative-experience-item {
    padding: 1.5rem;
  }

  .theme-creative-experience-role {
    font-size: 1.25rem;
  }

  .theme-creative-experience-header {
    gap: 1.5rem;
  }

  .theme-creative-experience-meta-section {
    align-items: flex-start;
  }

  .theme-creative-experience-duration-badge {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .theme-creative-experience-title {
    font-size: 1.75rem;
  }

  .theme-creative-experience-item {
    padding: 1rem;
  }

  .theme-creative-experience-role-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .theme-creative-experience-company-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* Animation for smooth appearance */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.theme-creative-experience-item {
  animation: fadeInUp 0.6s ease forwards;
}

.theme-creative-experience-item:nth-child(2) {
  animation-delay: 0.1s;
}

.theme-creative-experience-item:nth-child(3) {
  animation-delay: 0.2s;
}

.theme-creative-experience-item:nth-child(4) {
  animation-delay: 0.3s;
}
