"use client";
import { useEditorSafe } from "@/contexts/EditorContext";
import { SectionProps } from "@/lib/types";
import { Skill } from "@/lib/types";
import { Plus, Trash2, Code, Palette, Settings, Layers, Smartphone, BarChart3, Megaphone, Briefcase } from "lucide-react";

interface SkillItemProps {
    skill: Skill;
    isEditing: boolean;
    onUpdate: (id: string, field: keyof Skill, value: string) => void;
    onDelete: (id: string) => void;
}

const getCategoryIcon = (category: Skill['category']) => {
    switch (category) {
        case 'web-development': return <Code className="skills-category-icon skills-icon-web" />;
        case 'mobile-development': return <Smartphone className="skills-category-icon skills-icon-mobile" />;
        case 'design': return <Palette className="skills-category-icon skills-icon-design" />;
        case 'data-science': return <BarChart3 className="skills-category-icon skills-icon-data" />;
        case 'devops': return <Settings className="skills-category-icon skills-icon-devops" />;
        case 'marketing': return <Megaphone className="skills-category-icon skills-icon-marketing" />;
        case 'business': return <Briefcase className="skills-category-icon skills-icon-business" />;
        default: return <Layers className="skills-category-icon skills-icon-other" />;
    }
};

const getCategoryLabel = (category: Skill['category']) => {
    switch (category) {
        case 'web-development': return 'Web Development';
        case 'mobile-development': return 'Mobile Development';
        case 'design': return 'Design';
        case 'data-science': return 'Data Science';
        case 'devops': return 'DevOps';
        case 'marketing': return 'Marketing';
        case 'business': return 'Business';
        default: return 'Other';
    }
};

const getCategoryClass = (category: Skill['category']) => {
    switch (category) {
        case 'web-development': return 'skills-category-web';
        case 'mobile-development': return 'skills-category-mobile';
        case 'design': return 'skills-category-design';
        case 'data-science': return 'skills-category-data';
        case 'devops': return 'skills-category-devops';
        case 'marketing': return 'skills-category-marketing';
        case 'business': return 'skills-category-business';
        default: return 'skills-category-other';
    }
};

const SkillItem = ({ skill, isEditing, onUpdate, onDelete }: SkillItemProps) => {
    return (
        <div className={`skills-item ${getCategoryClass(skill.category)}`}>
            <div className="skills-item-content">
                {isEditing ? (
                    <input
                        type="text"
                        value={skill.name}
                        onChange={(e) => onUpdate(skill.id, 'name', e.target.value)}
                        className="skills-input"
                        placeholder="Enter skill name"
                    />
                ) : (
                    <span className="skills-name">{skill.name}</span>
                )}
            </div>

            {isEditing && (
                <div className="skills-controls">
                    <div className="skills-control-group">
                        <label className="skills-label">
                            Category:
                            <select
                                value={skill.category}
                                onChange={(e) => onUpdate(skill.id, 'category', e.target.value)}
                                className="skills-select"
                            >
                                <option value="web-development">Web Development</option>
                                <option value="mobile-development">Mobile Development</option>
                                <option value="design">Design</option>
                                <option value="data-science">Data Science</option>
                                <option value="devops">DevOps</option>
                                <option value="marketing">Marketing</option>
                                <option value="business">Business</option>
                                <option value="other">Other</option>
                            </select>
                        </label>
                        <button
                            onClick={() => onDelete(skill.id)}
                            className="skills-delete-btn"
                            title="Delete skill"
                        >
                            <Trash2 className="skills-delete-icon" />
                            Remove
                        </button>
                    </div>
                </div>
            )}
        </div>
    );
};

export function CreativeSkills({ isEditing, serverData }: SectionProps) {
    const context = useEditorSafe();
    const data = isEditing && context ? context.state.formData : serverData!;
    const dispatch = isEditing && context ? context.dispatch : null;

    const handleAddSkill = () => {
        if (dispatch) {
            const newSkill: Skill = {
                id: Date.now().toString(),
                name: 'New Skill',
                category: 'other'
            };

            // If we only have default skills, replace them with the new one
            const currentSkills = data.skills || [];
            const updatedSkills = currentSkills.length === 0
                ? [newSkill]
                : [...currentSkills, newSkill];

            dispatch({
                type: 'UPDATE_FIELD',
                payload: {
                    field: 'skills',
                    value: updatedSkills
                }
            });
        }
    };

    const handleUpdateSkill = (id: string, field: keyof Skill, value: string) => {
        if (dispatch) {
            const currentSkills = data.skills || [];
            const updatedSkills = currentSkills.map(skill =>
                skill.id === id ? { ...skill, [field]: value } : skill
            );
            dispatch({
                type: 'UPDATE_FIELD',
                payload: {
                    field: 'skills',
                    value: updatedSkills
                }
            });
        }
    };

    const handleDeleteSkill = (id: string) => {
        if (dispatch) {
            const currentSkills = data.skills || [];
            const updatedSkills = currentSkills.filter(skill => skill.id !== id);
            // When deleting, always update the actual data (even if it becomes empty)
            // The display logic will handle showing dummy when needed
            dispatch({
                type: 'UPDATE_FIELD',
                payload: {
                    field: 'skills',
                    value: updatedSkills
                }
            });
        }
    };


    const skills = data.skills || [];

    // Group skills by category with null check
    const groupedSkills = skills.reduce((acc, skill) => {
        if (!acc[skill.category]) {
            acc[skill.category] = [];
        }
        acc[skill.category].push(skill);
        return acc;
    }, {} as Record<Skill['category'], Skill[]>);

    return (
        <section id="skills" className="skills-section">
            <div className="skills-container">
                <div className="skills-header">
                    <h2 className="skills-title">Skills & Technologies</h2>
                    <div className="skills-subtitle">
                        Technologies and tools I work with
                    </div>
                </div>

                <div className="skills-content">
                    {Object.entries(groupedSkills).map(([category, skills]) => (
                        <div key={category} className="skills-category">
                            <div className="skills-category-header">
                                {getCategoryIcon(category as Skill['category'])}
                                <h3 className="skills-category-title">
                                    {getCategoryLabel(category as Skill['category'])}
                                </h3>
                            </div>
                            <div className="skills-grid">
                                {skills.map((skill) => (
                                    <SkillItem
                                        key={skill.id}
                                        skill={skill}
                                        isEditing={isEditing}
                                        onUpdate={handleUpdateSkill}
                                        onDelete={handleDeleteSkill}
                                    />
                                ))}
                            </div>
                        </div>
                    ))}

                    {isEditing && (
                        <div className="skills-add-container">
                            <button
                                onClick={handleAddSkill}
                                className="skills-add-btn"
                            >
                                <Plus className="skills-add-icon" />
                                Add New Skill
                            </button>
                        </div>
                    )}
                </div>
            </div>
        </section>
    );
};
