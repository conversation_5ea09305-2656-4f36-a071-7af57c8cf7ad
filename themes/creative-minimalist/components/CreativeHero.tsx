"use client";
import { useEditorSafe } from "@/contexts/EditorContext";
import { SectionProps } from "@/lib/types";
import { EditableText } from "@/components/ui/EditableText";
import { PortfolioData } from "@/lib/types";
import { Upload, Loader2, Download, FileText } from "lucide-react";
import { PortfolioImage } from "@/components/ui/PortfolioImage";
import { useIsExport } from "@/contexts/ExportContext";
import { useEffect } from "react";

// Helper function to strip HTML tags - consistent server/client rendering
const stripHtml = (html: string): string => {
    if (!html) return '';

    // Use regex to strip HTML tags - works consistently on server and client
    return html
        .replace(/<[^>]*>/g, '') // Remove HTML tags
        .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
        .replace(/&amp;/g, '&')  // Replace HTML entities
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/\s+/g, ' ')    // Normalize whitespace
        .trim();
};

interface ProfileImageUploaderProps {
    isEditing: boolean;
    data: PortfolioData;
    isUploadingProfile: boolean;
    onImageUpload: (options: { file: File; type: 'profile' | 'resume' | 'project'; id?: string }) => void;
}

const ProfileImageUploader = ({ isEditing, data, isUploadingProfile, onImageUpload }: ProfileImageUploaderProps) => (
    <div className="theme-creative-hero-image-container" id="top">
        <div className="theme-creative-hero-image-wrapper">
            <PortfolioImage
                isEditing={isEditing}
                src={data.profileImageUrl || 'https://placehold.co/320x320/f3f4f6/6b7280?text=Profile'}
                alt={data.userName}
                width={320}
                height={320}
                className="theme-creative-hero-image"
            />

            {isUploadingProfile && (
                <div className="theme-creative-hero-upload-loading">
                    <div className="theme-creative-hero-upload-loading-content">
                        <Loader2 className="theme-creative-hero-upload-spinner" />
                        <p className="theme-creative-hero-upload-text">Uploading...</p>
                    </div>
                </div>
            )}

            {isEditing && !isUploadingProfile && (
                <label htmlFor="profile-image-upload-creative" className="theme-creative-hero-upload-overlay">
                    <Upload className="theme-creative-hero-upload-icon" />
                    <span>Upload Photo</span>
                    <input
                        id="profile-image-upload-creative"
                        type="file"
                        className="theme-creative-hero-upload-input"
                        accept="image/*"
                        onChange={(e) => e.target.files && onImageUpload({ file: e.target.files[0], type: 'profile' })}
                    />
                </label>
            )}
        </div>
    </div>
);

interface HeroContentProps {
    isEditing: boolean;
    data: PortfolioData;
    handleUpdate: (field: keyof Omit<PortfolioData, 'projects'>, value: string) => void;
    isUploadingResume: boolean;
    onImageUpload: (options: { file: File; type: 'profile' | 'resume' | 'project'; id?: string }) => void;
    isExport: boolean;
}

const HeroContent = ({ isEditing, data, handleUpdate, isUploadingResume, onImageUpload }: HeroContentProps) => (
    <div className="theme-creative-hero-content">
        <div className="theme-creative-hero-text">
            <h1 className="theme-creative-hero-title">
                Hi, I&#39;m{' '}
                <EditableText
                    isEditing={isEditing}
                    tagName="span"
                    className="theme-creative-hero-name editable-field-gradient editable-field-creative"
                    initialValue={data.userName}
                    placeholder="Your Name"
                    onSave={(v) => handleUpdate('userName', v)}
                />
            </h1>
            <div className="theme-creative-hero-profession">
                <EditableText
                    isEditing={isEditing}
                    tagName="div"
                    className="theme-creative-hero-profession-text editable-field editable-field-creative"
                    initialValue={data.profession}
                    placeholder="Your Profession"
                    onSave={(v) => handleUpdate('profession', v)}
                />
            </div>
        </div>

        <div className="theme-creative-hero-description">
            <EditableText
                isEditing={isEditing}
                tagName="p"
                className="theme-creative-hero-description-text editable-field-large editable-field-creative"
                initialValue={data.about || "I create beautiful and functional digital experiences that make a difference."}
                placeholder="Brief description about yourself and what you do"
                onSave={(value) => handleUpdate('about', value)}
            />
        </div>

        <div className="theme-creative-hero-actions">
            <a
                href="#projects"
                className="theme-creative-btn theme-creative-btn-primary"
            >
                View My Work
            </a>

            {isEditing && (
                isUploadingResume ? (
                    <button disabled className="theme-creative-btn theme-creative-btn-secondary">
                        <Loader2 className="theme-creative-hero-action-icon theme-creative-spinner" />
                        Uploading...
                    </button>
                ) : (
                    <label className="theme-creative-btn theme-creative-btn-secondary theme-creative-hero-upload-btn">
                        <FileText className="theme-creative-hero-action-icon" />
                        <span>Upload Resume</span>
                        <input
                            id="resume-upload-creative"
                            type="file"
                            className="theme-creative-hero-upload-input"
                            accept=".pdf"
                            onChange={(e) => e.target.files && onImageUpload({ file: e.target.files[0], type: 'resume' })}
                        />
                    </label>
                )
            )}

            {data.resumeUrl && (
                <a
                    href={data.resumeUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="theme-creative-btn theme-creative-btn-outline"
                >
                    <Download className="theme-creative-hero-action-icon" />
                    <span>Download Resume</span>
                </a>
            )}
        </div>
    </div>
);

export function CreativeHero({ isEditing, serverData, onImageUpload }: SectionProps) {
    const context = useEditorSafe();
    const isExport = useIsExport();

    const data = isEditing && context ? context.state.formData : serverData!;
    const dispatch = isEditing && context ? context.dispatch : null;
    const isUploadingProfile = isEditing && context && context.state.isUploading?.type === 'profile';
    const isUploadingResume = isEditing && context && context.state.isUploading?.type === 'resume';

    // Clean existing HTML data on component mount
    useEffect(() => {
        if (isEditing && dispatch && data.about) {
            const cleanAbout = stripHtml(data.about);
            if (cleanAbout !== data.about) {
                dispatch({ type: 'UPDATE_FIELD', payload: { field: 'about', value: cleanAbout } });
            }
        }
        if (isEditing && dispatch && data.userName) {
            const cleanUserName = stripHtml(data.userName);
            if (cleanUserName !== data.userName) {
                dispatch({ type: 'UPDATE_FIELD', payload: { field: 'userName', value: cleanUserName } });
            }
        }
        if (isEditing && dispatch && data.profession) {
            const cleanProfession = stripHtml(data.profession);
            if (cleanProfession !== data.profession) {
                dispatch({ type: 'UPDATE_FIELD', payload: { field: 'profession', value: cleanProfession } });
            }
        }
    }, [isEditing, dispatch, data.about, data.userName, data.profession]);

    const handleUpdate = (field: keyof Omit<PortfolioData, 'projects'>, value: string) => {
        if (dispatch) {
            dispatch({ type: 'UPDATE_FIELD', payload: { field, value } });
        }
    };

    return (
        <section className="theme-creative-hero">
            <div className="theme-creative-hero-container">
                <div className="theme-creative-hero-layout">
                    <HeroContent
                        isEditing={isEditing}
                        data={data}
                        handleUpdate={handleUpdate}
                        isUploadingResume={isUploadingResume ?? false}
                        onImageUpload={onImageUpload ?? (() => { })}
                        isExport={isExport}
                    />
                    <ProfileImageUploader
                        isEditing={isEditing}
                        data={data}
                        isUploadingProfile={isUploadingProfile ?? false}
                        onImageUpload={onImageUpload ?? (() => { })}
                    />
                </div>
            </div>
        </section>
    );
};
