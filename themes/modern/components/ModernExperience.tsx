"use client";
import { useEditorSafe } from "@/contexts/EditorContext";
import { SectionProps } from "@/lib/types";

import { Experience } from "@/lib/types";
import { Plus, Trash2, Building2, MapPin, ExternalLink } from "lucide-react";

interface ExperienceItemProps {
    experience: Experience;
    isEditing: boolean;
    onUpdate: (id: string, field: keyof Experience, value: string) => void;
    onDelete: (id: string) => void;
}

const ExperienceItem = ({ experience, isEditing, onUpdate, onDelete }: ExperienceItemProps) => {
    return (
        <div className="theme-modern-experience-item">
            <div className="theme-modern-experience-timeline-dot"></div>
            <div className="theme-modern-experience-content">
                <div className="theme-modern-experience-header">
                    <div className="theme-modern-experience-title-group">
                        {isEditing ? (
                            <input
                                type="text"
                                value={experience.role}
                                onChange={(e) => onUpdate(experience.id, 'role', e.target.value)}
                                className="theme-modern-experience-role-input"
                                placeholder="Job Title"
                            />
                        ) : (
                            <h3 className="theme-modern-experience-role">{experience.role}</h3>
                        )}
                        <div className="theme-modern-experience-company-info">
                            <Building2 className="theme-modern-experience-icon" />
                            {isEditing ? (
                                <input
                                    type="text"
                                    value={experience.company}
                                    onChange={(e) => onUpdate(experience.id, 'company', e.target.value)}
                                    className="theme-modern-experience-company-input"
                                    placeholder="Company Name"
                                />
                            ) : (
                                <span className="theme-modern-experience-company">{experience.company}</span>
                            )}
                            {experience.companyUrl && !isEditing && (
                                <a
                                    href={experience.companyUrl}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="theme-modern-experience-company-link"
                                >
                                    <ExternalLink className="theme-modern-experience-link-icon" />
                                </a>
                            )}
                        </div>
                    </div>
                    <div className="theme-modern-experience-meta">
                        {isEditing ? (
                            <input
                                type="text"
                                value={experience.duration}
                                onChange={(e) => onUpdate(experience.id, 'duration', e.target.value)}
                                className="theme-modern-experience-duration-input"
                                placeholder="e.g., Jan 2020 - Present, 2 years, etc."
                            />
                        ) : (
                            <span className="theme-modern-experience-duration">{experience.duration}</span>
                        )}
                        {(experience.location || isEditing) && (
                            <div className="theme-modern-experience-location">
                                <MapPin className="theme-modern-experience-icon" />
                                {isEditing ? (
                                    <input
                                        type="text"
                                        value={experience.location || ''}
                                        onChange={(e) => onUpdate(experience.id, 'location', e.target.value)}
                                        className="theme-modern-experience-location-input"
                                        placeholder="Location (optional)"
                                    />
                                ) : (
                                    <span className="theme-modern-experience-location-text">{experience.location}</span>
                                )}
                            </div>
                        )}
                    </div>
                </div>
                
                {(experience.description || isEditing) && (
                    <div className="theme-modern-experience-description-container">
                        {isEditing ? (
                            <textarea
                                value={experience.description || ''}
                                onChange={(e) => onUpdate(experience.id, 'description', e.target.value)}
                                className="theme-modern-experience-description-input"
                                placeholder="Describe your key responsibilities and achievements in this role..."
                                rows={3}
                            />
                        ) : (
                            <p className="theme-modern-experience-description">{experience.description}</p>
                        )}
                    </div>
                )}

                {isEditing && (
                    <div className="theme-modern-experience-actions">
                        <button
                            onClick={() => onDelete(experience.id)}
                            className="theme-modern-experience-delete-btn"
                            title="Delete experience"
                        >
                            <Trash2 className="theme-modern-experience-delete-icon" />
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
};

export function ModernExperience({ isEditing, serverData }: SectionProps) {
    const context = useEditorSafe();
    const data = isEditing && context ? context.state.formData : serverData!;
    const dispatch = isEditing && context ? context.dispatch : null;

    const handleAddExperience = () => {
        if (dispatch) {
            const newExperience: Experience = {
                id: Date.now().toString(),
                role: 'New Role',
                company: 'Company Name',
                duration: 'Start Date - End Date',
                description: 'Describe your key responsibilities and achievements...',
                location: 'Location'
            };

            // If we only have the default experience, replace it with the new one
            const currentExperiences = data.experiences || [];
            const updatedExperiences = currentExperiences.length === 0
                ? [newExperience]
                : [...currentExperiences, newExperience];

            dispatch({
                type: 'UPDATE_FIELD',
                payload: {
                    field: 'experiences',
                    value: updatedExperiences
                }
            });
        }
    };

    const handleUpdateExperience = (id: string, field: keyof Experience, value: string) => {
        if (dispatch) {
            const currentExperiences = data.experiences || [];
            const updatedExperiences = currentExperiences.map(exp =>
                exp.id === id ? { ...exp, [field]: value } : exp
            );
            dispatch({
                type: 'UPDATE_FIELD',
                payload: {
                    field: 'experiences',
                    value: updatedExperiences
                }
            });
        }
    };

    const handleDeleteExperience = (id: string) => {
        if (dispatch) {
            const currentExperiences = data.experiences || [];
            const updatedExperiences = currentExperiences.filter(exp => exp.id !== id);
            dispatch({
                type: 'UPDATE_FIELD',
                payload: {
                    field: 'experiences',
                    value: updatedExperiences
                }
            });
        }
    };


    const experiences = data.experiences || [];

    return (
        <section id="experience" className="theme-modern-experience">
            <div className="theme-modern-experience-container">
                <h2 className="theme-modern-experience-title">Experience</h2>
                
                <div className="theme-modern-experience-timeline">
                    {experiences.map((experience) => (
                        <ExperienceItem
                            key={experience.id}
                            experience={experience}
                            isEditing={isEditing}
                            onUpdate={handleUpdateExperience}
                            onDelete={handleDeleteExperience}
                        />
                    ))}
                    
                    {isEditing && (
                        <div className="theme-modern-experience-add-container">
                            <button
                                onClick={handleAddExperience}
                                className="theme-modern-experience-add-btn"
                            >
                                <Plus className="theme-modern-experience-add-icon" />
                                Add Experience
                            </button>
                        </div>
                    )}
                </div>
            </div>
        </section>
    );
};
