"use client";
import { useEditorSafe } from "@/contexts/EditorContext";
import { SectionProps } from "@/lib/types";
import { EditableText } from "@/components/ui/EditableText";
import { PortfolioData } from "@/lib/types";
import { PortfolioImage } from "@/components/ui/PortfolioImage";
import { useEffect } from "react";

// Helper function to strip HTML tags - consistent server/client rendering
const stripHtml = (html: string): string => {
    if (!html) return '';

    // Use regex to strip HTML tags - works consistently on server and client
    return html
        .replace(/<[^>]*>/g, '') // Remove HTML tags
        .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
        .replace(/&amp;/g, '&')  // Replace HTML entities
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/\s+/g, ' ')    // Normalize whitespace
        .trim();
};

export function ModernAbout({ isEditing, serverData }: SectionProps) {
    const context = useEditorSafe();
    const data = isEditing && context ? context.state.formData : serverData!;
    const dispatch = isEditing && context ? context.dispatch : null;

    // Clean existing HTML data on component mount
    useEffect(() => {
        if (isEditing && dispatch && data.bio) {
            const cleanBio = stripHtml(data.bio);
            if (cleanBio !== data.bio) {
                dispatch({ type: 'UPDATE_FIELD', payload: { field: 'bio', value: cleanBio } });
            }
        }
    }, [isEditing, dispatch, data.bio]);

    const handleUpdate = (field: keyof Omit<PortfolioData, 'projects'>, value: string) => {
        if (dispatch) {
            dispatch({ type: 'UPDATE_FIELD', payload: { field, value } });
        }
    };

    return (
        <section id="about" className="theme-modern-about">
            <div className="theme-modern-about-container">
                <div className="theme-modern-about-content">
                    <EditableText
                        isEditing={isEditing}
                        tagName="h2"
                        className="theme-modern-about-title"
                        initialValue="About Me"
                        onSave={() => {}} // Static title
                    />
                    <EditableText
                        isEditing={isEditing}
                        tagName="p"
                        className="theme-modern-about-text"
                        initialValue={data.bio || "Tell your story here. Share your background, experience, and what drives you in your professional journey."}
                        onSave={(value) => handleUpdate('bio', value)}
                    />
                </div>
                <div className="theme-modern-about-image-container">
                    <PortfolioImage
                        isEditing={isEditing}
                        src={data.profileImageUrl || 'https://placehold.co/400x400/f3f4f6/6b7280?text=About'}
                        alt={`${data.userName} - About`}
                        width={400}
                        height={400}
                        className="theme-modern-about-image"
                    />
                </div>
            </div>
        </section>
    );
};
