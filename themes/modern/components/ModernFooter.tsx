"use client";
import { useEditorSafe } from "@/contexts/EditorContext";
import { SectionProps } from "@/lib/types";

export function ModernFooter({ isEditing, serverData }: SectionProps) {
    const context = useEditorSafe();
    const data = isEditing && context ? context.state.formData : serverData!;
    const currentYear = new Date().getFullYear();

    return (
        <footer className="theme-modern-footer">
            <div className="theme-modern-footer-container">
                <div className="theme-modern-footer-content">
                    <p className="theme-modern-footer-text">
                        © {currentYear} {data.userName || "Portfolio"}. All rights reserved.
                    </p>
                    <p className="theme-modern-footer-attribution">
                        Powered by Profolify
                    </p>
                </div>
            </div>
        </footer>
    );
};
