"use client";
import { ModernNavbar } from "./ModernNavbar";
import { ModernHero } from "./ModernHero";
import { ModernAbout } from "./ModernAbout";
import { ModernExperience } from "./ModernExperience";
import { ModernSkills } from "./ModernSkills";
import { ModernProjects } from "./ModernProjects";
import { ModernContact } from "./ModernContact";
import { ModernFooter } from "./ModernFooter";
import { ProfolifyThemeProps } from "@/lib/types";

export function ModernTheme({ isEditing, serverData, onImageUpload }: ProfolifyThemeProps) {
    return (
        <div className="theme-modern-root">
            <ModernNavbar isEditing={isEditing} serverData={serverData} />
            <main>
                <ModernHero isEditing={isEditing} serverData={serverData} onImageUpload={onImageUpload} />
                <ModernAbout isEditing={isEditing} serverData={serverData} />
                <ModernExperience isEditing={isEditing} serverData={serverData} />
                <ModernSkills isEditing={isEditing} serverData={serverData} />
                <ModernProjects isEditing={isEditing} serverData={serverData} onImageUpload={onImageUpload} />
                <ModernContact isEditing={isEditing} serverData={serverData} />
            </main>
            <ModernFooter isEditing={isEditing} serverData={serverData} />
        </div>
    );
}
