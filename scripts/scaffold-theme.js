#!/usr/bin/env node

/**
 * Theme Scaffolding Utility
 * 
 * This script automatically generates the modular folder structure
 * and boilerplate files for new themes.
 * 
 * Usage:
 *   node scripts/scaffold-theme.js <theme-name>
 */

const fs = require('fs');
const path = require('path');

/**
 * Generate main modular CSS file
 */
function generateModularCSS(themeName) {
  const displayName = themeName.charAt(0).toUpperCase() + themeName.slice(1).replace('-', ' ');
  
  return `/* ${displayName} Theme - Modular CSS */

/* Import Component Styles */
@import url('./components/navbar.css');
@import url('./components/hero.css');
@import url('./components/about.css');
@import url('./components/experience.css');
@import url('./components/skills.css');
@import url('./components/projects.css');
@import url('./components/contact.css');
@import url('./components/footer.css');

/* Base styles and reset */
.theme-${themeName}-root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  line-height: 1.6;
  color: #333;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  scroll-behavior: smooth;
}

.theme-${themeName}-root *,
.theme-${themeName}-root *::before,
.theme-${themeName}-root *::after {
  box-sizing: inherit;
}

/* Container utilities */
.theme-${themeName}-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .theme-${themeName}-container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .theme-${themeName}-container {
    padding: 0 2rem;
  }
}

/* Utility classes */
.theme-${themeName}-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
}

.theme-${themeName}-btn-primary {
  background: #3b82f6;
  color: white;
}

.theme-${themeName}-btn-primary:hover {
  background: #2563eb;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}`;
}

/**
 * Create a new theme with modular structure
 */
function createTheme(themeName) {
  if (!themeName) {
    console.error('❌ Theme name is required!');
    console.log('Usage: node scripts/scaffold-theme.js <theme-name>');
    return false;
  }
  
  // Validate theme name
  if (!/^[a-z][a-z0-9-]*$/.test(themeName)) {
    console.error('❌ Theme name must be lowercase, start with a letter, and contain only letters, numbers, and hyphens.');
    return false;
  }
  
  const themeDir = path.join(process.cwd(), 'themes', themeName);
  const componentsDir = path.join(themeDir, 'components');
  
  // Check if theme already exists
  if (fs.existsSync(themeDir)) {
    console.error(`❌ Theme '${themeName}' already exists!`);
    return false;
  }
  
  console.log(`🎨 Creating new theme: ${themeName}`);
  
  // Create directories
  fs.mkdirSync(themeDir, { recursive: true });
  fs.mkdirSync(componentsDir, { recursive: true });
  
  // Create basic component CSS files
  const basicComponents = ['navbar', 'hero', 'about', 'experience', 'skills', 'projects', 'contact', 'footer'];
  
  console.log('📄 Creating component files...');
  for (const component of basicComponents) {
    const filePath = path.join(componentsDir, `${component}.css`);
    const content = `/* ${themeName} Theme - ${component.charAt(0).toUpperCase() + component.slice(1)} Component */

.theme-${themeName}-${component} {
  /* Add your ${component} styles here */
  padding: 2rem 0;
}

.theme-${themeName}-${component}-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.theme-${themeName}-${component}-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
}`;
    
    fs.writeFileSync(filePath, content);
    console.log(`   ✅ ${component}.css`);
  }
  
  // Create main modular CSS file
  const modularCSSPath = path.join(themeDir, `${themeName}-modular.css`);
  const modularContent = generateModularCSS(themeName);
  fs.writeFileSync(modularCSSPath, modularContent);
  console.log(`   ✅ ${themeName}-modular.css`);
  
  console.log(`\n🎉 Theme '${themeName}' created successfully!`);
  console.log(`\n📁 Theme structure:`);
  console.log(`   themes/${themeName}/`);
  console.log(`   ├── ${themeName}-modular.css`);
  console.log(`   └── components/`);
  basicComponents.forEach(comp => {
    console.log(`       ├── ${comp}.css`);
  });
  
  console.log(`\n🚀 Next steps:`);
  console.log(`   1. Customize the CSS files in themes/${themeName}/components/`);
  console.log(`   2. Create React components in themes/${themeName}/components/`);
  console.log(`   3. Register the theme in themes/theme-registry.ts`);
  console.log(`   4. Run 'npm run sync-themes' to compile and sync`);
  
  return true;
}

// Run the script if called directly
if (require.main === module) {
  const themeName = process.argv[2];
  const success = createTheme(themeName);
  if (!success) {
    process.exit(1);
  }
}

module.exports = { createTheme };
