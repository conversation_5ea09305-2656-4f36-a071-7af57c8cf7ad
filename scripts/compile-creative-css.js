#!/usr/bin/env node

/**
 * Creative Minimalist CSS Compiler
 * 
 * This script compiles the modular CSS files into a single CSS file
 * for proper export functionality.
 */

const fs = require('fs');
const path = require('path');

function compileCreativeMinimalistCSS() {
  console.log('🎨 Compiling Creative Minimalist CSS...');
  
  const themeDir = path.join(process.cwd(), 'themes/creative-minimalist');
  const componentsDir = path.join(themeDir, 'components');
  const modularFile = path.join(themeDir, 'creative-minimalist-modular.css');
  const compiledFile = path.join(themeDir, 'creative-minimalist-compiled.css');
  
  // Read the modular CSS file
  if (!fs.existsSync(modularFile)) {
    console.error('❌ Modular CSS file not found:', modularFile);
    return false;
  }
  
  let modularContent = fs.readFileSync(modularFile, 'utf8');
  
  // Component files to import
  const componentFiles = [
    'navbar.css',
    'hero.css', 
    'about.css',
    'experience.css',
    'skills.css',
    'projects.css',
    'contact.css',
    'footer.css'
  ];
  
  let compiledContent = '/* Creative Minimalist Theme - Compiled CSS */\n\n';
  
  // Add each component's CSS
  for (const componentFile of componentFiles) {
    const componentPath = path.join(componentsDir, componentFile);
    
    if (fs.existsSync(componentPath)) {
      console.log(`📄 Adding ${componentFile}...`);
      const componentContent = fs.readFileSync(componentPath, 'utf8');
      
      compiledContent += `/* ===== ${componentFile.toUpperCase().replace('.CSS', '')} STYLES ===== */\n`;
      compiledContent += componentContent;
      compiledContent += '\n\n';
    } else {
      console.warn(`⚠️  Component file not found: ${componentFile}`);
    }
  }
  
  // Add the base styles from modular file (everything after the imports)
  const lines = modularContent.split('\n');
  let addingBaseStyles = false;
  let baseStyles = '';
  
  for (const line of lines) {
    if (line.trim().startsWith('@import')) {
      continue; // Skip import statements
    }
    
    if (line.trim().includes('Base styles') || line.trim().includes('theme-creative-root')) {
      addingBaseStyles = true;
    }
    
    if (addingBaseStyles) {
      baseStyles += line + '\n';
    }
  }
  
  if (baseStyles.trim()) {
    compiledContent += '/* ===== BASE STYLES ===== */\n';
    compiledContent += baseStyles;
  }
  
  // Write the compiled CSS
  fs.writeFileSync(compiledFile, compiledContent);
  
  const stats = fs.statSync(compiledFile);
  console.log(`✅ Compiled CSS created: ${compiledFile} (${stats.size} bytes)`);
  
  return true;
}

// Run the compilation
if (require.main === module) {
  const success = compileCreativeMinimalistCSS();
  if (!success) {
    process.exit(1);
  }
}

module.exports = { compileCreativeMinimalistCSS };
