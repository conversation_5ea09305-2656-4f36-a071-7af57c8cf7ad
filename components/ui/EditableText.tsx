"use client";
import React, { useState, useEffect, useRef } from 'react';
import ContentEditable from 'react-contenteditable';
import { cn } from '@/lib/utils';

interface EditableTextProps {
    initialValue: string;
    isEditing: boolean;
    onSave: (value: string) => void;
    className?: string;
    tagName?: 'h1' | 'h2' | 'h3' | 'p' | 'div' | 'span';
    placeholder?: string;
}

// Helper function to strip HTML tags and decode HTML entities
// Uses regex-based approach for consistent server/client rendering
const stripHtml = (html: string): string => {
    if (!html) return '';

    // Use regex to strip HTML tags - works consistently on server and client
    return html
        .replace(/<[^>]*>/g, '') // Remove HTML tags
        .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
        .replace(/&amp;/g, '&')  // Replace HTML entities
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/\s+/g, ' ')    // Normalize whitespace
        .trim();
};

export function EditableText({ initialValue, isEditing, onSave, className, tagName = 'div', placeholder }: EditableTextProps) {
    // Always use stripped HTML for consistent rendering
    const cleanInitialValue = stripHtml(initialValue || '');
    const [text, setText] = useState(cleanInitialValue);
    const isFocused = useRef(false);

    useEffect(() => {
        // Only update the local state from props if the component is not focused.
        // This prevents the cursor from jumping during typing.
        if (!isFocused.current) {
            const cleanValue = stripHtml(initialValue || '');
            setText(cleanValue);
        }
    }, [initialValue]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement> | { target: { value: string } }) => {
        const rawText = e.target.value;
        setText(rawText);

        // Strip HTML tags before saving to prevent HTML injection and formatting issues
        const cleanText = stripHtml(rawText);
        onSave(cleanText);
    };

    const handlePaste = (e: React.ClipboardEvent) => {
        e.preventDefault();
        // Get plain text from clipboard
        const plainText = e.clipboardData.getData('text/plain');

        // Update the text state with clean text
        const cleanText = stripHtml(plainText);
        setText(cleanText);
        onSave(cleanText);
    };

    if (isEditing) {
        return (
            <ContentEditable
                html={text} // Use already cleaned text
                onChange={handleChange}
                onPaste={handlePaste}
                onFocus={() => isFocused.current = true}
                onBlur={() => isFocused.current = false}
                tagName={tagName}
                placeholder={placeholder}
                className={cn(
                    "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-0 rounded-md m-0 p-2 cursor-text border border-gray-300 bg-gray-50 text-gray-600 transition-all duration-200 hover:bg-gray-100 hover:border-gray-400",
                    "empty:before:content-[attr(placeholder)] empty:before:text-gray-400 empty:before:italic",
                    className
                )}
            />
        );
    }
    // Use the same cleaned text for consistent server/client rendering
    return React.createElement(tagName, { className }, cleanInitialValue);
}