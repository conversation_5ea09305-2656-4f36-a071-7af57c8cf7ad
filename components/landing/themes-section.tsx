"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON>, Eye, Download, Palette } from "lucide-react";
import { useRouter } from "next/navigation";

const themes = [
  {
    id: "modern",
    name: "Modern",
    description: "Clean, professional design perfect for developers, designers, and creative professionals.",
    image: "/themes/modern-preview.jpg", // You'll need to add these images
    features: ["Clean Typography", "Responsive Design", "Professional Layout", "Dark/Light Mode"],
    category: "Professional"
  },
  {
    id: "creative-minimalist", 
    name: "Creative Minimalist",
    description: "Minimalist design with creative touches, ideal for artists, photographers, and creative minds.",
    image: "/themes/creative-preview.jpg", // You'll need to add these images
    features: ["Minimalist Design", "Creative Elements", "Portfolio Focus", "Mobile Optimized"],
    category: "Creative"
  }
];

export function ThemesSection() {
  const router = useRouter();

  return (
    <section
      id="themes"
      className="py-24 lg:py-32 bg-backgroundPrimary relative overflow-hidden"
    >
      {/* Clean Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-20 w-64 h-64 bg-gradient-to-r from-brandPrimary/4 to-brandSecondary/4 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-brandSecondary/3 to-brandAccent/3 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-brandSecondary/10 border border-brandSecondary/20 mb-8">
            <Palette className="w-4 h-4 text-brandSecondary" />
            <span className="text-sm font-semibold text-brandSecondary">
              Beautiful Themes
            </span>
          </div>

          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
            <span className="gradient-text">Choose Your Style</span>
            <br />
            <span className="text-textPrimary">2 Themes Available</span>
          </h2>

          <p className="text-lg md:text-xl text-textSecondary max-w-3xl mx-auto leading-relaxed">
            Start with our carefully crafted themes. Each one is fully responsive, 
            professionally designed, and ready to showcase your work beautifully.
          </p>
        </div>

        {/* Themes Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto mb-16">
          {themes.map((theme) => (
            <div
              key={theme.id}
              className="group bg-backgroundSecondary/50 rounded-2xl border border-borderPrimary/30 overflow-hidden hover:border-brandPrimary/30 transition-all duration-300 hover:shadow-lg hover:shadow-brandPrimary/5"
            >
              {/* Theme Preview Image */}
              <div className="relative h-64 bg-gradient-to-br from-brandPrimary/5 to-brandSecondary/5 flex items-center justify-center">
                {/* Placeholder for theme preview */}
                <div className="w-full h-full bg-gradient-to-br from-brandPrimary/10 to-brandSecondary/10 flex items-center justify-center">
                  <div className="text-center">
                    <Palette className="w-12 h-12 text-brandPrimary mx-auto mb-2" />
                    <p className="text-textSecondary text-sm">{theme.name} Preview</p>
                  </div>
                </div>
              </div>

              {/* Theme Info */}
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="text-xl font-bold text-textPrimary">{theme.name}</h3>
                      <span className="px-2 py-1 text-xs font-semibold bg-brandPrimary/10 text-brandPrimary rounded-full">
                        {theme.category}
                      </span>
                    </div>
                    <p className="text-textSecondary text-sm leading-relaxed">
                      {theme.description}
                    </p>
                  </div>
                </div>

                {/* Features */}
                <div className="mb-6">
                  <div className="flex flex-wrap gap-2">
                    {theme.features.map((feature) => (
                      <span
                        key={feature}
                        className="px-3 py-1 text-xs bg-backgroundPrimary/50 text-textSecondary rounded-full border border-borderPrimary/30"
                      >
                        {feature}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Actions */}
                <div className="flex gap-3">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1 hover:border-brandPrimary/50 transition-colors duration-200"
                  >
                    <Eye className="w-4 h-4 mr-2" />
                    Preview
                  </Button>
                  <Button
                    onClick={() => router.push("/login")}
                    size="sm"
                    className="flex-1 bg-gradient-to-r from-brandPrimary to-brandSecondary text-white hover:shadow-lg hover:shadow-brandPrimary/25 transition-all duration-300"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Use Theme
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center bg-gradient-to-r from-brandPrimary/5 to-brandSecondary/5 rounded-2xl p-8 border border-brandPrimary/10">
          <h3 className="text-2xl md:text-3xl font-bold mb-4">
            <span className="gradient-text">More Themes Coming Soon</span>
          </h3>
          
          <p className="text-textSecondary mb-6 max-w-2xl mx-auto">
            We&apos;re working on more beautiful themes including Business Professional,
            Portfolio Showcase, and Creative Agency designs.
          </p>

          <Button
            onClick={() => router.push("/login")}
            className="bg-gradient-to-r from-brandPrimary to-brandSecondary text-white font-semibold px-8 py-4 text-lg rounded-xl hover:shadow-lg hover:shadow-brandPrimary/25 transition-all duration-300"
          >
            <span className="flex items-center gap-2">
              Start with Current Themes
              <ArrowRight className="w-5 h-5" />
            </span>
          </Button>
        </div>
      </div>
    </section>
  );
}
