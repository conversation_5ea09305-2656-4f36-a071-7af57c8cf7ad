"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";

import { useState, useEffect } from "react";
import { Menu, X } from "lucide-react";

export function LandingNavbar() {
  const router = useRouter();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Add scroll spy effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const navLinks = [
    { href: "#home", label: "Home" },
    { href: "#features", label: "Features" },
    { href: "#themes", label: "Themes" },
  ];

  return (
    <header
      className={`fixed top-0 w-full right-0 z-50 transition-all duration-300 ${
        isScrolled
          ? "bg-backgroundPrimary/95 border-b border-borderPrimary/30 shadow-lg backdrop-blur-xl"
          : "bg-transparent"
      }`}
    >


      <nav className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Clean Logo */}
          <Link
            href="/"
            className="flex items-center gap-2 relative z-10"
          >
            <div className="w-8 h-8 lg:w-10 lg:h-10 rounded-xl bg-gradient-to-br from-brandPrimary to-brandSecondary flex items-center justify-center">
              <span className="text-white font-bold text-sm lg:text-base">
                P
              </span>
            </div>
            <span className="font-bold text-xl lg:text-2xl gradient-text">
              Profolify
            </span>
          </Link>

          {/* Clean Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-1">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className="px-4 py-2 text-textSecondary hover:text-brandPrimary transition-colors duration-200 font-medium rounded-lg hover:bg-brandPrimary/5"
              >
                {link.label}
              </Link>
            ))}
          </div>

          {/* Clean Right side actions */}
          <div className="flex items-center space-x-3">
            {/* Clean Desktop CTA */}
            <div className="hidden sm:flex items-center">
              <Button
                onClick={() => router.push("/login")}
                className="bg-gradient-to-r from-brandPrimary to-brandSecondary text-white font-semibold px-6 py-2.5 rounded-xl hover:shadow-lg hover:shadow-brandPrimary/25 transition-all duration-300"
              >
                Get Started
              </Button>
            </div>

            {/* Clean Mobile menu button */}
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden p-2.5 rounded-xl hover:bg-brandPrimary/10 transition-colors duration-200"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              {isMobileMenuOpen ? (
                <X className="w-5 h-5" />
              ) : (
                <Menu className="w-5 h-5" />
              )}
            </Button>
          </div>
        </div>

        {/* Clean Mobile Navigation */}
        <div
          className={`md:hidden transition-all duration-300 ${
            isMobileMenuOpen
              ? "max-h-96 opacity-100"
              : "max-h-0 opacity-0 pointer-events-none"
          }`}
        >
          <div className="border-t border-borderPrimary/30 mt-4 pt-6 pb-6 space-y-2 bg-backgroundPrimary/95 rounded-b-xl mx-4 px-4">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className="block text-textSecondary hover:text-brandPrimary transition-colors duration-200 font-medium py-3 px-4 rounded-lg hover:bg-brandPrimary/5"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                {link.label}
              </Link>
            ))}
            <div className="pt-4 border-t border-borderPrimary/20">
              <Button
                onClick={() => {
                  router.push("/login");
                  setIsMobileMenuOpen(false);
                }}
                className="w-full bg-gradient-to-r from-brandPrimary to-brandSecondary text-white font-semibold py-3 rounded-xl hover:shadow-lg hover:shadow-brandPrimary/25 transition-all duration-300"
              >
                Get Started
              </Button>
            </div>
          </div>
        </div>
      </nav>
    </header>
  );
}
