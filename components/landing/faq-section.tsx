"use client";

import {
  Accordion,
  Accordion<PERSON>ontent,
  Accordion<PERSON><PERSON>,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { HelpCircle, MessageCircle } from "lucide-react";
import { Button } from "@/components/ui/button";

const faqs = [
  {
    question: "Is the free tier really free forever?",
    answer:
      "Yes! You can create and publish portfolios with basic themes completely free, forever. No hidden costs, no time limits. We believe everyone should have access to professional portfolio creation tools.",
  },
  {
    question: "What's the difference between basic and premium themes?",
    answer:
      "Basic themes offer clean, professional layouts that are perfect for most users. Premium themes include advanced animations, interactive elements, custom layouts, and extensive customization options like custom CSS/JavaScript support.",
  },
  {
    question: "Can I export my portfolio as HTML?",
    answer:
      "Yes! Free users get basic HTML export functionality. Pro users get complete HTML/CSS/JavaScript export with optimized assets, SEO meta tags, and deployment-ready code that works on any hosting platform.",
  },
  {
    question: "How does the live publishing feature work?",
    answer:
      "With one click, your portfolio is instantly published to a custom URL (e.g., yourname.profolify.com). Changes are reflected in real-time. Pro users can also connect their custom domains for a fully branded experience.",
  },
  {
    question: "What file types can I upload?",
    answer:
      "Free users can upload common image formats (JPG, PNG, GIF) up to 5MB per file. Pro users get expanded file type support including PDFs, videos, and larger file sizes up to 100MB per file.",
  },
  {
    question: "Can I use my own domain name?",
    answer:
      "Custom domain support is available with Pro plans. You can connect any domain you own (e.g., www.yourname.com) and we'll handle all the technical setup including SSL certificates.",
  },
  {
    question: "Is there a limit to how many portfolios I can create?",
    answer:
      "No limits! Both free and Pro users can create unlimited portfolios. Each portfolio gets its own unique URL and can be customized independently.",
  },
  {
    question: "When will Pro features be available?",
    answer:
      "Pro features are launching in Q2 2024. Existing users will be notified first and can upgrade seamlessly. Your free portfolios will remain unchanged and fully functional.",
  },
  {
    question: "What kind of support do you offer?",
    answer:
      "Free users get access to our community support forum and comprehensive documentation. Pro users receive priority email support with guaranteed 24-hour response times.",
  },
  {
    question: "Can I cancel my Pro subscription anytime?",
    answer:
      "Absolutely! You can cancel your Pro subscription at any time. Your portfolios will remain live, and you'll continue to have access to Pro features until the end of your billing cycle.",
  },
];

export function FAQSection() {
  return (
    <section className="py-24 lg:py-32 bg-backgroundPrimary relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-b from-backgroundSecondary via-backgroundPrimary to-backgroundSecondary"></div>
      <div className="absolute top-20 left-10 w-64 h-64 bg-gradient-to-r from-brandPrimary/4 to-brandSecondary/4 rounded-full blur-3xl"></div>
      <div className="absolute bottom-20 right-10 w-80 h-80 bg-gradient-to-r from-brandAccent/3 to-brandPrimary/3 rounded-full blur-3xl"></div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-3 px-6 py-3 rounded-2xl glass-effect border border-brandPrimary/30 backdrop-blur-2xl mb-8">
            <HelpCircle className="w-5 h-5 text-brandPrimary" />
            <span className="text-sm font-semibold text-brandPrimary">
              Frequently Asked Questions
            </span>
          </div>

          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
            <span className="text-textPrimary">Got</span>
            <span className="gradient-text"> Questions?</span>
            <br />
            <span className="text-textPrimary">We&#39;ve Got Answers</span>
          </h2>

          <p className="text-xl md:text-2xl text-textSecondary max-w-3xl mx-auto leading-relaxed">
            Everything you need to know about Profolify, from getting started to
            advanced features.
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          {/* FAQ Accordion */}
          <div className="glass-effect rounded-3xl p-8 lg:p-12 border border-borderPrimary/50 backdrop-blur-2xl mb-12">
            <Accordion type="single" collapsible className="space-y-6">
              {faqs.map((faq, index) => (
                <AccordionItem
                  key={index}
                  value={`item-${index}`}
                  className="border border-borderPrimary/30 rounded-2xl px-6 py-2 hover:border-brandPrimary/40 transition-colors duration-300"
                >
                  <AccordionTrigger className="text-left text-lg font-semibold text-textPrimary hover:text-brandPrimary transition-colors py-6 [&[data-state=open]]:text-brandPrimary">
                    {faq.question}
                  </AccordionTrigger>
                  <AccordionContent className="text-textSecondary leading-relaxed pb-6 pt-2">
                    {faq.answer}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>

          {/* Contact Support CTA */}
          <div className="text-center">
            <div className="glass-effect rounded-3xl p-8 lg:p-10 border border-borderPrimary/50 backdrop-blur-2xl">
              <div className="flex items-center justify-center mb-6">
                <div className="w-16 h-16 rounded-2xl bg-gradient-to-r from-brandPrimary to-brandSecondary flex items-center justify-center shadow-lg shadow-brandPrimary/25">
                  <MessageCircle className="w-8 h-8 text-white" />
                </div>
              </div>

              <h3 className="text-2xl font-bold text-textPrimary mb-4">
                Still have questions?
              </h3>

              <p className="text-textSecondary mb-6 max-w-2xl mx-auto">
                Can&#39;t find the answer you&#39;re looking for? Our friendly support
                team is here to help you get the most out of Profolify.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  variant="outline"
                  className="px-8 py-3 rounded-2xl border-2 border-borderPrimary/50 hover:border-brandPrimary/50 glass-effect backdrop-blur-2xl"
                >
                  Browse Documentation
                </Button>

                <Button className="bg-gradient-to-r from-brandPrimary to-brandSecondary text-white px-8 py-3 rounded-2xl border-0 shadow-lg shadow-brandPrimary/25">
                  Contact Support
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
